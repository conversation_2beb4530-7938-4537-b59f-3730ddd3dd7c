
/* 自定义品牌主题 */
[data-theme="custom-brand"] {
  --color-primary: #2563eb;
  --color-secondary: #7c3aed;
  --color-accent: #06b6d4;
  --color-primary-content: #ffffff;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  
  /* 阴影效果 */
  --shadow-primary: 0 4px 20px rgba(25, 118, 210, 0.3);
}

/* 深色主题 */
[data-theme="custom-brand-dark"] {
  --color-primary: #2563eb;
  --color-secondary: #7c3aed;
  --color-accent: #06b6d4;
  
  --color-base-100: #1a1a1a;
  --color-base-200: #2d2d2d;
  --color-base-300: #404040;
  --color-base-content: #ffffff;
}

/* 现代化组件样式 */
.modern-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}
