#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义模型配置测试脚本
"""

import subprocess
import time
import sys
import os

def test_custom_provider():
    """测试自定义提供商功能"""
    print("🧪 自定义模型配置测试")
    print("=" * 50)
    
    print("请按以下步骤测试:")
    print()
    print("1. **重启AI助手软件**")
    print("   - 确保所有修改生效")
    print()
    print("2. **添加自定义aihubmix模型**:")
    print("   - 进入设置 → 模型配置")
    print("   - 点击'添加'按钮")
    print("   - 名称: 输入 'My AIHUBMIX'")
    print("   - 提供商: 选择 'aihubmix'")
    print("   - API地址: https://api.aihubmix.com/v1")
    print("   - API密钥: 输入您的真实密钥")
    print("   - 保存配置")
    print()
    print("3. **验证配置**:")
    print("   - 检查提供商类型是否保持为'aihubmix'")
    print("   - 检查是否能正常选择该模型")
    print()
    print("4. **测试消息发送**:")
    print("   - 选择刚添加的自定义模型")
    print("   - 发送测试消息: '今天几月几号'")
    print("   - 观察是否还出现401或502错误")
    print()
    print("5. **测试删除功能**:")
    print("   - 尝试删除刚添加的自定义模型")
    print("   - 检查是否能正常删除")
    print()
    print("🔍 预期结果:")
    print("✅ 提供商类型保持为'aihubmix'")
    print("✅ 不再出现401认证错误")
    print("✅ 不再出现502网关错误")
    print("✅ 能正常发送和接收消息")
    print("✅ 能正常删除自定义模型")
    print()
    print("如果仍有问题，请查看软件日志中的[DEBUG]输出")

if __name__ == "__main__":
    test_custom_provider()
