#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import uuid
import json
import time
import traceback
import sqlite3
import threading
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path

from aipyapp.aipy.mcp_tool import MCPToolManager
from aipyapp.aipy.cache import get_default_cache, cache_key
from aipyapp.aipy.plugin import event_bus
from aipyapp.aipy.llm import CLIENTS, Client, ClientManager
from aipyapp.aipy.task import Task
from aipyapp.aipy.diagnose import Diagnose
from aipyapp.aipy import prompt


from common import EventType, SystemEventType, Status, print_event

stderr = sys.stderr

class AttrDict(dict):
    def __init__(self, *args, **kwargs):
        super(AttrDict, self).__init__(*args, **kwargs)
        for key, value in self.items():
            if isinstance(value, dict):
                self[key] = AttrDict(value)

    def __getattr__(self, name):
        try:
            return self[name]
        except KeyError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

@dataclass
class DummyConsole:
    file: Any = None

@dataclass
class TaskManger:
    envs: Dict[str, str] = dict
    gui: Optional[bool] = False
    settings: Optional[Dict[str, Any]] = None
    current: Optional[Client] = None
    cwd: Optional[str] = Path(os.getcwd())
    console = DummyConsole()


class AipyClient(Client):
    def __init__(self, config):
        # 离线模式：检查配置类型，避免强制使用Trustoken
        if config.get("type") == "offline" or not config.get("api_key"):
            # 离线模式或无API密钥时，创建空的LLM管理器
            manager = ClientManager(AttrDict({"llm": {}}))
            self.client_type = "offline"
        else:
            # 保持原有逻辑用于向后兼容
            manager = ClientManager(AttrDict({"llm": {"trustoken": {**config, "default": True}}}))
            self.client_type = "trust"
        super().__init__(manager)

    def use(self, config):
        if self.current and self.current._params:
            params = self.current._params
        client_type = config.get("type", "trust")
        Client = CLIENTS.get(client_type)
        if not Client:
            return False

        model = config.pop("model")
        provider_id = config.pop("provider_id") or 0

        self.current = Client(config)

        if params and params.get("extra_headers"):
            self.current._params["extra_headers"] = params["extra_headers"]

        # 彻底修复502错误：对于aihubmix等第三方API，完全跳过x-custom-conf-id逻辑
        # 检查所有可能的URL字段名
        base_url = config.get("base_url", "") or config.get("baseUrl", "") or config.get("api_base", "")
        print(f"[DEBUG] provider_id={provider_id}, base_url={base_url}")
        print(f"[DEBUG] config keys: {list(config.keys())}")

        # 检查是否为aihubmix API
        is_aihubmix = base_url and "aihubmix.com" in base_url

        if provider_id > 0 and not is_aihubmix:
            # 只对非aihubmix的API添加x-custom-conf-id
            extra_headers = self.current._params.get("extra_headers", {})
            extra_headers["x-custom-conf-id"] = str(provider_id)
            self.current._params["extra_headers"] = extra_headers
            print(f"[DEBUG] Added x-custom-conf-id: {provider_id}")
        else:
            if is_aihubmix:
                print(f"[DEBUG] Completely skipped x-custom-conf-id for aihubmix (URL: {base_url})")
            else:
                print(f"[DEBUG] Skipped x-custom-conf-id (provider_id={provider_id})")

        self.current._model = model
        self.client_type = client_type

        return self.current

class AipyTask(Task):
    MAX_ROUNDS = 16

    def __init__(self, task_id = uuid.uuid4().hex, envs = {}, settings = {}, provider = {}):
        manager = TaskManger(envs=envs, gui=False, settings=settings)
        manager.cwd = Path(settings.get("workdir"))
        super().__init__(manager)
        self.cwd = manager.cwd
        self.task_id = task_id
        self.client = AipyClient(provider)
        self.diagnose = Diagnose.create(settings)
        self.cid = ""

    def box(self, *args, **kwargs):
        pass

    def process_reply(self, markdown):
        parse_mcp = self.mcp is not None
        ret = self.code_blocks.parse(markdown, parse_mcp=parse_mcp)
        if not ret:
            return None

        json_str = json.dumps(ret, ensure_ascii=False, indent=2, default=str)

        if 'call_tool' in ret:
            # 有可能MCP调用会按照代码格式返回，这种情况下存在exec_blocks和call_tool两个键,也可能会有erros字段
            # 优先处理call_tool
            return self.process_mcp_reply(ret['call_tool'])

        errors = ret.get('errors')
        if errors:
            event_bus('result', errors)
            feed_back = f"# 消息解析错误\n{json_str}"
            ret = self.chat(feed_back)
        elif 'exec_blocks' in ret:
            ret = self.process_code_reply(ret['exec_blocks'])
        else:
            event_bus('result', ret)
            ret = None
        return ret

    def process_code_reply(self, exec_blocks):
        results = []
        for block in exec_blocks:
            event_bus('exec', block)
            result = self.runner(block)
            results.append(result)
            event_bus('result', result, block=block)
        msg = prompt.get_results_prompt(results)
        feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        return self.chat(feed_back)

    def process_mcp_reply(self, json_content):
        """处理 MCP 工具调用的回复"""
        call_tool = json.loads(json_content)
        event = {
            'id': uuid.uuid4().hex,
            'type': 'mcp',
            "tool": call_tool['name'],
            'arguments': call_tool.get('arguments', {}),
            'done': False,
        }
        event_bus('tool_call', event)
        result = self.mcp.call_tool(call_tool['name'], call_tool.get('arguments', {}))
        event["result"] = result
        event["done"] = True
        event_bus('tool_call', event)

        msg = prompt.get_mcp_result_prompt(result)
        feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        feedback_response = self.chat(feed_back)
        return feedback_response

    def chat(self, instruction, *, system_prompt=None):
        if self.is_stopped():
            return ""
        print_event(
            event_type=EventType.SYSTEM,
            content={"type": SystemEventType.STATUS, "value": Status.THINKING})
        self.cid = uuid.uuid4().hex
        return super().chat(instruction, system_prompt=system_prompt)

    def save(self, *args, **kwargs):
        pass


class AipyMCP(MCPToolManager):
    def __init__(self, config_path, tt_api_key):
        super().__init__(config_path, tt_api_key)

    def list_tools(self, mcp_type="user", force_load=False):
        if force_load:
            return super().list_tools(mcp_type, force_load)

        ret = {}
        cache = get_default_cache()
        with cache._lock:
            with sqlite3.connect(cache.db_path) as conn:
                cursor = conn.execute('SELECT key, value FROM cache')
                for (key, value) in cursor.fetchall():
                    key_parts = key.split(":")
                    if len(key_parts) < 3 or key_parts[0] != "mcp_tool":
                        continue
                    key = key_parts[1]
                    if mcp_type == "user" and key.startswith("Trustoken-"):
                        continue
                    elif mcp_type == "sys" and not key.startswith("Trustoken-"):
                        continue
                    try:
                        ret[key] = json.loads(value)
                    except:
                        traceback.print_exc(file=stderr)
                        continue
                if mcp_type == "sys":
                    if not ret.get("Trustoken-search"):
                        tools = [{"name": "get_my_ip", "title": None, "description": "获取当前客户端的公网IP地址以及IP的地理位置信息", "inputSchema": {"properties": {}, "type": "object"}, "outputSchema": None, "annotations": {"title": None, "readOnlyHint": False, "destructiveHint": True, "idempotentHint": False, "openWorldHint": True}, "meta": None, "server": "Trustoken-search", "id": "Trustoken-search.get_my_ip"}, {"name": "search", "title": None, "description": "联网搜索服务，可以用于检索新闻和模型不知道的最新信息", "inputSchema": {"properties": {"queryString": {"description": "搜索的关键词，不支持'site：'语法", "type": "string"}, "timeRange": {"default": "NoLimit", "description": "The time range for the search, e.g., 'OneDay'", "enum": ["OneDay", "OneWeek", "OneMonth", "OneYear", "NoLimit"], "type": "string"}}, "type": "object"}, "outputSchema": None, "annotations": {"title": None, "readOnlyHint": False, "destructiveHint": True, "idempotentHint": False, "openWorldHint": True}, "meta": None, "server": "Trustoken-search", "id": "Trustoken-search.search"}]
                        conn.execute(
                            'INSERT OR IGNORE INTO cache (key, value, expire_time, created_time) VALUES (?, ?, ?, ?)',
                            (
                                f"mcp_tool:Trustoken-search:{cache_key(self.sys_mcp.get('Trustoken-search'))}",
                                json.dumps(tools, ensure_ascii=False),
                                time.time() + 60 * 60 * 24 * 30,
                                time.time()
                            )
                        )
                        ret["Trustoken-search"] = tools
                    if not ret.get("Trustoken-search"):
                        tools = [{"name": "maps_direction_bicycling", "title": None, "description": "骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_bicycling"}, {"name": "maps_direction_driving", "title": None, "description": "驾车路径规划 API 可以根据用户起终点经纬度坐标规划以小客车、轿车通勤出行的方案，并且返回通勤方案的数据。", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_driving"}, {"name": "maps_direction_transit_integrated", "title": None, "description": "根据用户起终点经纬度坐标规划综合各类公共（火车、公交、地铁）交通方式的通勤方案，并且返回通勤方案的数据，跨城场景下必须传起点城市与终点城市", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}, "city": {"type": "string", "description": "公共交通规划起点城市"}, "cityd": {"type": "string", "description": "公共交通规划终点城市"}}, "required": ["origin", "destination", "city", "cityd"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_transit_integrated"}, {"name": "maps_direction_walking", "title": None, "description": "根据输入起点终点经纬度坐标规划100km 以内的步行通勤方案，并且返回通勤方案的数据", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经度，纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经度，纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_walking"}, {"name": "maps_distance", "title": None, "description": "测量两个经纬度坐标之间的距离,支持驾车、步行以及球面距离测量", "inputSchema": {"type": "object", "properties": {"origins": {"type": "string", "description": "起点经度，纬度，可以传多个坐标，使用竖线隔离，比如120,30|120,31，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "终点经度，纬度，坐标格式为：经度，纬度"}, "type": {"type": "string", "description": "距离测量类型,1代表驾车距离测量，0代表直线距离测量，3步行距离测量"}}, "required": ["origins", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_distance"}, {"name": "maps_geo", "title": None, "description": "将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标", "inputSchema": {"type": "object", "properties": {"address": {"type": "string", "description": "待解析的结构化地址信息"}, "city": {"type": "string", "description": "指定查询的城市"}}, "required": ["address"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_geo"}, {"name": "maps_regeocode", "title": None, "description": "将一个高德经纬度坐标转换为行政区划地址信息", "inputSchema": {"type": "object", "properties": {"location": {"type": "string", "description": "经纬度"}}, "required": ["location"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_regeocode"}, {"name": "maps_ip_location", "title": None, "description": "IP 定位根据用户输入的 IP 地址，定位 IP 的所在位置", "inputSchema": {"type": "object", "properties": {"ip": {"type": "string", "description": "IP地址"}}, "required": ["ip"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_ip_location"}, {"name": "maps_schema_personal_map", "title": None, "description": "用于行程规划结果在高德地图展示。将行程规划位置点按照行程顺序填入lineList，返回结果为高德地图打开的URI链接，该结果不需总结，直接返回！", "inputSchema": {"type": "object", "properties": {"orgName": {"type": "string", "description": "行程规划地图小程序名称"}, "lineList": {"type": "array", "description": "行程列表", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "行程名称描述（按行程顺序）"}, "pointInfoList": {"type": "array", "description": "行程目标位置点描述", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "行程目标位置点名称"}, "lon": {"type": "number", "description": "行程目标位置点经度"}, "lat": {"type": "number", "description": "行程目标位置点纬度"}, "poiId": {"type": "string", "description": "行程目标位置点POIID"}}, "required": ["name", "lon", "lat", "poiId"]}}}, "required": ["title", "pointInfoList"]}}}, "required": ["orgName", "lineList"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_personal_map"}, {"name": "maps_around_search", "title": None, "description": "周边搜，根据用户传入关键词以及坐标location，搜索出radius半径范围的POI", "inputSchema": {"type": "object", "properties": {"keywords": {"type": "string", "description": "搜索关键词"}, "location": {"type": "string", "description": "中心点经度纬度"}, "radius": {"type": "string", "description": "搜索半径"}}, "required": ["keywords", "location"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_around_search"}, {"name": "maps_search_detail", "title": None, "description": "查询关键词搜或者周边搜获取到的POI ID的详细信息", "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "关键词搜或者周边搜获取到的POI ID"}}, "required": ["id"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_search_detail"}, {"name": "maps_text_search", "title": None, "description": "关键字搜索 API 根据用户输入的关键字进行 POI 搜索，并返回相关的信息", "inputSchema": {"type": "object", "properties": {"keywords": {"type": "string", "description": "查询关键字"}, "city": {"type": "string", "description": "查询城市"}, "citylimit": {"type": "boolean", "default": False, "description": "是否限制城市范围内搜索，默认不限制"}}, "required": ["keywords"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_text_search"}, {"name": "maps_schema_navi", "title": None, "description": " Schema唤醒客户端-导航页面，用于根据用户输入终点信息，返回一个拼装好的客户端唤醒URI，用户点击该URI即可唤起对应的客户端APP。唤起客户端后，会自动跳转到导航页面。", "inputSchema": {"type": "object", "properties": {"lon": {"type": "string", "description": "终点经度"}, "lat": {"type": "string", "description": "终点纬度"}}, "required": ["lon", "lat"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_navi"}, {"name": "maps_schema_take_taxi", "title": None, "description": "根据用户输入的起点和终点信息，返回一个拼装好的客户端唤醒URI，直接唤起高德地图进行打车。直接展示生成的链接，不需要总结", "inputSchema": {"type": "object", "properties": {"slon": {"type": "string", "description": "起点经度"}, "slat": {"type": "string", "description": "起点纬度"}, "sname": {"type": "string", "description": "起点名称"}, "dlon": {"type": "string", "description": "终点经度"}, "dlat": {"type": "string", "description": "终点纬度"}, "dname": {"type": "string", "description": "终点名称"}}, "required": ["dlon", "dlat", "dname"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_take_taxi"}, {"name": "maps_weather", "title": None, "description": "根据城市名称或者标准adcode查询指定城市的天气", "inputSchema": {"type": "object", "properties": {"city": {"type": "string", "description": "城市名称或者adcode"}}, "required": ["city"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_weather"}]
                        conn.execute(
                            'INSERT OR IGNORE INTO cache (key, value, expire_time, created_time) VALUES (?, ?, ?, ?)',
                            (
                                f"mcp_tool:Trustoken-map:{cache_key(self.sys_mcp.get('Trustoken-map'))}",
                                json.dumps(tools, ensure_ascii=False),
                                time.time() + 60 * 60 * 24 * 30,
                                time.time()
                            )
                        )
                        ret["Trustoken-map"] = tools

        if ret:
            # 异步后台执行父类list_tools，不等待
            threading.Thread(
                target=super().list_tools,
                args=(mcp_type, force_load),
                daemon=True
            ).start()

        return ret
