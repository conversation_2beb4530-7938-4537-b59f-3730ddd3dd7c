/* Trudecide AI Assistant - Custom Brand Theme */

/* 主题色彩定义 */
:root {
  /* Trudecide 品牌色彩 */
  --trudecide-primary: #2563eb;
  --trudecide-primary-dark: #1d4ed8;
  --trudecide-secondary: #7c3aed;
  --trudecide-accent: #06b6d4;
  --trudecide-success: #10b981;
  --trudecide-warning: #f59e0b;
  --trudecide-error: #ef4444;
  
  /* 渐变色 */
  --trudecide-gradient-primary: linear-gradient(135deg, #2563eb, #7c3aed);
  --trudecide-gradient-secondary: linear-gradient(135deg, #06b6d4, #2563eb);
  --trudecide-gradient-accent: linear-gradient(135deg, #7c3aed, #06b6d4);
  
  /* 阴影效果 */
  --trudecide-shadow-sm: 0 1px 2px 0 rgba(37, 99, 235, 0.05);
  --trudecide-shadow-md: 0 4px 6px -1px rgba(37, 99, 235, 0.1);
  --trudecide-shadow-lg: 0 10px 15px -3px rgba(37, 99, 235, 0.1);
  --trudecide-shadow-xl: 0 20px 25px -5px rgba(37, 99, 235, 0.1);
  
  /* 动画时长 */
  --trudecide-transition-fast: 0.15s;
  --trudecide-transition-normal: 0.3s;
  --trudecide-transition-slow: 0.5s;
}

/* Trudecide 亮色主题 */
[data-theme="trudecide"] {
  --color-primary: var(--trudecide-primary);
  --color-primary-content: #ffffff;
  --color-secondary: var(--trudecide-secondary);
  --color-secondary-content: #ffffff;
  --color-accent: var(--trudecide-accent);
  --color-accent-content: #ffffff;
  
  --color-base-100: #ffffff;
  --color-base-200: #f8fafc;
  --color-base-300: #e2e8f0;
  --color-base-content: #1f2937;
  
  --color-success: var(--trudecide-success);
  --color-warning: var(--trudecide-warning);
  --color-error: var(--trudecide-error);
}

/* Trudecide 深色主题 */
[data-theme="trudecide-dark"] {
  --color-primary: var(--trudecide-primary);
  --color-primary-content: #ffffff;
  --color-secondary: var(--trudecide-secondary);
  --color-secondary-content: #ffffff;
  --color-accent: var(--trudecide-accent);
  --color-accent-content: #ffffff;
  
  --color-base-100: #0f172a;
  --color-base-200: #1e293b;
  --color-base-300: #334155;
  --color-base-content: #f1f5f9;
  
  --color-success: var(--trudecide-success);
  --color-warning: var(--trudecide-warning);
  --color-error: var(--trudecide-error);
}

/* 现代化组件样式 */
.trudecide-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(37, 99, 235, 0.1);
  border-radius: 16px;
  box-shadow: var(--trudecide-shadow-lg);
  transition: all var(--trudecide-transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.trudecide-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--trudecide-shadow-xl);
  border-color: rgba(37, 99, 235, 0.2);
}

/* 渐变按钮 */
.trudecide-btn-gradient {
  background: var(--trudecide-gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  letter-spacing: 0.025em;
  transition: all var(--trudecide-transition-normal) ease;
  position: relative;
  overflow: hidden;
}

.trudecide-btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--trudecide-transition-slow) ease;
}

.trudecide-btn-gradient:hover::before {
  left: 100%;
}

.trudecide-btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

/* 输入框样式 */
.trudecide-input {
  background: rgba(248, 250, 252, 0.8);
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all var(--trudecide-transition-normal) ease;
  backdrop-filter: blur(8px);
}

.trudecide-input:focus {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--trudecide-primary);
  box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
  outline: none;
}

/* 加载动画 */
.trudecide-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(37, 99, 235, 0.3);
  border-radius: 50%;
  border-top-color: var(--trudecide-primary);
  animation: trudecide-spin 1s ease-in-out infinite;
}

@keyframes trudecide-spin {
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲效果 */
.trudecide-pulse {
  animation: trudecide-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes trudecide-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 滑入动画 */
.trudecide-slide-in {
  animation: trudecide-slide-in var(--trudecide-transition-normal) ease-out;
}

@keyframes trudecide-slide-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡入动画 */
.trudecide-fade-in {
  animation: trudecide-fade-in var(--trudecide-transition-normal) ease-out;
}

@keyframes trudecide-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 悬浮效果 */
.trudecide-hover-lift {
  transition: all var(--trudecide-transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}

.trudecide-hover-lift:hover {
  transform: translateY(-6px);
  box-shadow: var(--trudecide-shadow-xl);
}

/* 玻璃态效果 */
.trudecide-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 深色主题下的玻璃态 */
[data-theme="trudecide-dark"] .trudecide-glass {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* 品牌标识样式 */
.trudecide-brand {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
  background: var(--trudecide-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 状态指示器 */
.trudecide-status-online {
  position: relative;
}

.trudecide-status-online::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(--trudecide-success);
  border-radius: 50%;
  border: 2px solid var(--color-base-100);
  animation: trudecide-pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trudecide-card {
    border-radius: 12px;
    margin: 0.5rem;
  }
  
  .trudecide-btn-gradient {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .trudecide-card {
    border-width: 2px;
    border-color: var(--color-base-content);
  }
  
  .trudecide-input {
    border-width: 2px;
    border-color: var(--color-base-content);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .trudecide-card,
  .trudecide-btn-gradient,
  .trudecide-input,
  .trudecide-hover-lift {
    transition: none;
  }
  
  .trudecide-loading,
  .trudecide-pulse,
  .trudecide-slide-in,
  .trudecide-fade-in {
    animation: none;
  }
}
