# 🧪 自定义提供商问题调试检查清单

## 📋 测试前准备

- [ ] 已重新打包应用程序 (asar pack extracted_app resources\app.asar)
- [ ] 已完全重启AI助手软件
- [ ] 准备好有效的aihubmix API密钥
- [ ] 启用了调试模式或控制台输出

## 🔧 问题1：模型删除测试

### 测试步骤：
1. [ ] 添加一个测试用的自定义aihubmix模型
   - 名称：`Test-Delete-AIHUBMIX`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：任意测试密钥

2. [ ] 保存配置后，尝试删除该模型

3. [ ] 观察调试输出：
   - [ ] 看到 `[OFFLINE] Deleting provider locally: xxx`
   - [ ] 看到 `[OFFLINE] Found provider to delete: {...}`
   - [ ] 看到 `[OFFLINE] Provider deleted successfully`
   - [ ] 看到 `[OFFLINE] Related meta configs deleted`

### 预期结果：
- [ ] ✅ 模型从列表中完全消失
- [ ] ✅ 不再出现在模型选择中
- [ ] ✅ 没有删除错误信息

### 如果删除失败：
- [ ] 检查错误信息：`Provider xxx not found`
- [ ] 检查是否有权限问题
- [ ] 尝试重启软件后再删除

## 🔧 问题2：401认证错误测试

### 测试步骤：
1. [ ] 添加自定义aihubmix模型（使用真实API密钥）
   - 名称：`My-AIHUBMIX`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：您的真实密钥

2. [ ] 保存配置，确认提供商类型显示为"aihubmix"

3. [ ] 选择该模型，发送测试消息：`今天几月几号`

4. [ ] 观察调试输出：
   - [ ] 看到 `[DEBUG] Provider config being sent: {...}`
   - [ ] 看到 `[DEBUG] Custom config loaded: {...}`
   - [ ] 看到 `[DEBUG] Database record: {...}`
   - [ ] 检查配置中的api_key字段是否有值

### 预期结果：
- [ ] ✅ 不再出现401认证错误
- [ ] ✅ 能正常接收回复
- [ ] ✅ 提供商类型保持为"aihubmix"

### 如果仍有401错误：
- [ ] 检查调试输出中的api_key字段是否为空
- [ ] 检查customConfig是否正确加载
- [ ] 确认API密钥本身是否有效

## 🔧 问题3：提供商类型保持测试

### 测试步骤：
1. [ ] 添加自定义aihubmix模型
2. [ ] 保存后立即检查提供商类型

### 预期结果：
- [ ] ✅ 提供商类型显示为"aihubmix"（不是"openai"）

### 如果类型错误：
- [ ] 检查providerInfoList中的配置
- [ ] 确认修复是否正确应用

## 📊 调试信息收集

### 关键调试输出：
```
[OFFLINE] Creating provider locally: {...}
[OFFLINE] Provider created successfully with ID: xxx
[DEBUG] Provider config being sent: {...}
[DEBUG] Custom config loaded: {...}
[DEBUG] Database record: {...}
[OFFLINE] Deleting provider locally: xxx
[OFFLINE] Found provider to delete: {...}
[OFFLINE] Provider deleted successfully
```

### 需要检查的字段：
- [ ] customConfig.apiKey 是否有值
- [ ] customConfig.baseUrl 是否正确
- [ ] customConfig.type 是否为"aihubmix"
- [ ] 最终配置中的api_key字段是否正确

## 🎯 成功标准

### 完全成功：
- [ ] ✅ 能正常添加aihubmix自定义模型
- [ ] ✅ 提供商类型保持为"aihubmix"
- [ ] ✅ 能正常删除自定义模型
- [ ] ✅ 不再出现401认证错误
- [ ] ✅ 能正常发送接收消息

### 部分成功：
- [ ] 部分功能正常，记录具体问题

### 需要进一步修复：
- [ ] 记录具体错误信息和调试输出

## 📝 测试报告模板

**测试时间：** ___________

**删除功能测试：**
- 结果：[ ] 成功 [ ] 失败
- 错误信息：___________

**401认证测试：**
- 结果：[ ] 成功 [ ] 失败
- 错误信息：___________

**提供商类型测试：**
- 结果：[ ] 成功 [ ] 失败
- 显示类型：___________

**关键调试输出：**
```
（粘贴相关的[DEBUG]输出）
```

**总体评价：**
[ ] 完全成功 [ ] 部分成功 [ ] 需要进一步修复

**备注：**
___________
