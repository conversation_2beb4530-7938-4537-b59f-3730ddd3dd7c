# 🎨 AI助手软件品牌重塑使用指南

## 📋 快速开始

### 1. 准备工作
确保您已经解压了AI助手软件的asar文件：
```bash
asar extract resources\app.asar extracted_app
```

### 2. 配置品牌信息
编辑 `brand_config.json` 文件，设置您的品牌信息：

```json
{
  "app_name": "my-ai-assistant",
  "display_name": "My AI Assistant",
  "brand_name": "MyBrand",
  "brand_name_zh": "我的品牌",
  "author": "My Company",
  "homepage": "https://my-company.com",
  "team_name": "My Team"
}
```

### 3. 运行品牌替换工具
```bash
python brand_replacement_tool.py
```

### 4. 重新打包应用
```bash
asar pack extracted_app resources\app.asar
```

---

## 🔧 详细配置说明

### 基础配置项
| 配置项 | 说明 | 示例 |
|--------|------|------|
| `app_name` | 应用程序内部名称 | `"my-ai-assistant"` |
| `display_name` | 显示名称 | `"My AI Assistant"` |
| `brand_name` | 英文品牌名 | `"MyBrand"` |
| `brand_name_zh` | 中文品牌名 | `"我的品牌"` |
| `author` | 作者/公司名 | `"My Company"` |
| `homepage` | 官网地址 | `"https://my-company.com"` |
| `team_name` | 团队名称 | `"My Team"` |

### 主题配置
```json
"custom_theme": {
  "primary_color": "#1976d2",     // 主色调
  "secondary_color": "#dc004e",   // 辅助色
  "accent_color": "#00bcd4",      // 强调色
  "background_color": "#ffffff",  // 背景色
  "text_color": "#212529"         // 文字色
}
```

### 高级选项
```json
"advanced_options": {
  "enable_dark_theme": true,      // 启用深色主题
  "enable_animations": true,      // 启用动画效果
  "enable_gradients": true,       // 启用渐变色
  "remove_about_page": false,     // 移除关于页面
  "custom_css_file": "custom.css" // 自定义CSS文件
}
```

---

## 🖼️ Logo和图标替换

### 准备图标文件
1. **应用图标** (icon.png)
   - 尺寸：256x256 像素
   - 格式：PNG
   - 背景：透明

2. **Logo文件** (logo.svg)
   - 格式：SVG矢量图
   - 建议：简洁设计，适合小尺寸显示

3. **Logo图片** (logo.webp)
   - 格式：WebP或PNG
   - 尺寸：建议200x60像素

### 替换步骤
```bash
# 1. 备份原文件
cp extracted_app/out/renderer/image/icon.png icon_backup.png
cp extracted_app/out/renderer/image/logo-borderless.svg logo_backup.svg

# 2. 替换新文件
cp your_icon.png extracted_app/out/renderer/image/icon.png
cp your_logo.svg extracted_app/out/renderer/image/logo-borderless.svg
cp your_logo.webp extracted_app/out/renderer/image/logo.webp
```

---

## 🎨 自定义样式

### 创建自定义CSS
创建 `custom.css` 文件：

```css
/* 自定义品牌样式 */
.brand-header {
  background: linear-gradient(135deg, #1976d2, #dc004e);
  color: white;
  padding: 1rem;
  border-radius: 8px;
}

.brand-button {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.brand-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
```

### 应用自定义样式
在 `brand_config.json` 中指定：
```json
"advanced_options": {
  "custom_css_file": "custom.css"
}
```

---

## 🧪 测试和验证

### 功能测试清单
- [ ] 应用正常启动
- [ ] 界面显示正确的品牌名称
- [ ] Logo和图标正确显示
- [ ] 所有菜单功能正常
- [ ] 设置页面可以打开
- [ ] 任务创建和执行正常

### 视觉检查
- [ ] 色彩搭配协调
- [ ] 文字清晰可读
- [ ] 响应式布局正常
- [ ] 深色主题（如启用）正常

### 多平台测试
- [ ] Windows 10/11
- [ ] macOS
- [ ] Linux
- [ ] 不同分辨率屏幕

---

## 🔄 恢复和回滚

### 使用备份恢复
如果需要恢复到原始版本：

```bash
# 1. 找到备份目录（格式：backup_YYYYMMDD_HHMMSS）
ls -la backup_*

# 2. 恢复文件
cp backup_20241226_143022/package.json extracted_app/
cp backup_20241226_143022/index.html extracted_app/out/renderer/
cp -r backup_20241226_143022/assets/* extracted_app/out/renderer/assets/
cp -r backup_20241226_143022/image/* extracted_app/out/renderer/image/

# 3. 重新打包
asar pack extracted_app resources\app.asar
```

### 重新开始
如果需要重新进行品牌替换：

```bash
# 1. 删除当前修改
rm -rf extracted_app

# 2. 重新解压原始文件
asar extract resources\app.asar.backup extracted_app

# 3. 修改配置后重新运行
python brand_replacement_tool.py
```

---

## ❓ 常见问题

### Q: 运行工具后应用无法启动？
A: 检查以下几点：
1. 确保备份文件完整
2. 检查配置文件格式是否正确
3. 验证替换的文件路径是否存在
4. 尝试使用备份恢复

### Q: 品牌文本没有完全替换？
A: 可能的原因：
1. JavaScript文件名可能不同，检查assets目录
2. 某些文本可能在其他文件中，需要手动查找
3. 文本可能被压缩或编码，需要特殊处理

### Q: 自定义主题不生效？
A: 检查：
1. CSS文件是否正确创建
2. 主题名称是否正确应用
3. 浏览器缓存是否需要清理

### Q: Logo显示异常？
A: 确认：
1. 图片格式是否支持
2. 文件大小是否合适
3. 路径是否正确

---

## 📞 技术支持

如果遇到问题，请：

1. **检查日志**：查看工具运行时的输出信息
2. **查看报告**：阅读生成的 `brand_replacement_report.md`
3. **验证配置**：确认 `brand_config.json` 格式正确
4. **使用备份**：必要时恢复到原始状态

---

## 🎉 完成后的步骤

品牌重塑完成后，建议：

1. **全面测试**：在不同环境下测试应用功能
2. **用户反馈**：收集用户对新界面的反馈
3. **持续优化**：根据使用情况调整设计
4. **文档更新**：更新相关的用户文档和帮助信息

祝您品牌重塑成功！🚀
