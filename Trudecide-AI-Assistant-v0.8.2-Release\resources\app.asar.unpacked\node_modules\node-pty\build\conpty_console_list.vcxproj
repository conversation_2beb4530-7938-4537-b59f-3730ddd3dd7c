<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D3D121CF-5306-ACDE-C833-06B52E372C1E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>conpty_console_list</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <SpectreMitigation>Spectre</SpectreMitigation>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\bin\;$(MSBuildProjectDirectory)\..\bin\</ExecutablePath>
    <IgnoreImportLibrary>true</IgnoreImportLibrary>
    <IntDir>$(Configuration)\obj\$(ProjectName)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName).node</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\37.2.4\include\node;C:\Users\<USER>\.electron-gyp\37.2.4\src;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\config;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\uv\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\zlib;C:\Users\<USER>\.electron-gyp\37.2.4\deps\v8\include;..\node_modules\node-addon-api;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/guard:cf /w34244 /we4267 /ZH:SHA_256 /Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <EnablePREfast>true</EnablePREfast>
      <ExceptionHandling>Sync</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=conpty_console_list;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;V8_COMPRESS_POINTERS;V8_COMPRESS_POINTERS_IN_SHARED_CAGE;V8_31BIT_SMIS_ON_64BIT_ARCH;V8_ENABLE_SANDBOX;V8_EXTERNAL_CODE_SPACE;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;NAPI_CPP_EXCEPTIONS;_HAS_EXCEPTIONS=1;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\37.2.4\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/guard:cf /LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\37.2.4\include\node;C:\Users\<USER>\.electron-gyp\37.2.4\src;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\config;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\uv\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\zlib;C:\Users\<USER>\.electron-gyp\37.2.4\deps\v8\include;..\node_modules\node-addon-api;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=conpty_console_list;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;V8_COMPRESS_POINTERS;V8_COMPRESS_POINTERS_IN_SHARED_CAGE;V8_31BIT_SMIS_ON_64BIT_ARCH;V8_ENABLE_SANDBOX;V8_EXTERNAL_CODE_SPACE;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;NAPI_CPP_EXCEPTIONS;_HAS_EXCEPTIONS=1;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\37.2.4\include\node;C:\Users\<USER>\.electron-gyp\37.2.4\src;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\config;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\uv\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\zlib;C:\Users\<USER>\.electron-gyp\37.2.4\deps\v8\include;..\node_modules\node-addon-api;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/guard:cf /w34244 /we4267 /ZH:SHA_256 /Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <EnablePREfast>true</EnablePREfast>
      <ExceptionHandling>Sync</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Full</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=conpty_console_list;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;V8_COMPRESS_POINTERS;V8_COMPRESS_POINTERS_IN_SHARED_CAGE;V8_31BIT_SMIS_ON_64BIT_ARCH;V8_ENABLE_SANDBOX;V8_EXTERNAL_CODE_SPACE;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;NAPI_CPP_EXCEPTIONS;_HAS_EXCEPTIONS=1;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\.electron-gyp\\37.2.4\\x64\\node.lib&quot;</AdditionalDependencies>
      <AdditionalOptions>/guard:cf /LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\.electron-gyp\37.2.4\include\node;C:\Users\<USER>\.electron-gyp\37.2.4\src;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\config;C:\Users\<USER>\.electron-gyp\37.2.4\deps\openssl\openssl\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\uv\include;C:\Users\<USER>\.electron-gyp\37.2.4\deps\zlib;C:\Users\<USER>\.electron-gyp\37.2.4\deps\v8\include;..\node_modules\node-addon-api;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=conpty_console_list;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;_FILE_OFFSET_BITS=64;ELECTRON_ENSURE_CONFIG_GYPI;USING_ELECTRON_CONFIG_GYPI;V8_COMPRESS_POINTERS;V8_COMPRESS_POINTERS_IN_SHARED_CAGE;V8_31BIT_SMIS_ON_64BIT_ARCH;V8_ENABLE_SANDBOX;V8_EXTERNAL_CODE_SPACE;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;OPENSSL_NO_ASM;NAPI_CPP_EXCEPTIONS;_HAS_EXCEPTIONS=1;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\binding.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\win\conpty_console_list.cc">
      <ObjectFileName>$(IntDir)\src\win\conpty_console_list.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\aipy\desktop\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="node_modules\node-addon-api\node_addon_api_except.vcxproj">
      <Project>{85CD1BF8-628E-A22B-4A40-828310879580}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
