@echo off
chcp 65001 >nul
echo 🔧 AI助手软件缓存清除脚本
echo ================================

echo.
echo 📋 正在清除软件缓存...
echo.

REM 1. 关闭可能正在运行的软件进程
echo ⏹️  正在关闭软件进程...
taskkill /f /im "AI助手.exe" 2>nul
taskkill /f /im "aipyapp.exe" 2>nul
taskkill /f /im "electron.exe" 2>nul
timeout /t 3 /nobreak >nul

REM 2. 清除用户配置目录缓存
echo 🗂️  正在清除用户配置缓存...
set USER_CONFIG_DIR=%USERPROFILE%\.aipyapp
if exist "%USER_CONFIG_DIR%" (
    echo    - 删除: %USER_CONFIG_DIR%
    rmdir /s /q "%USER_CONFIG_DIR%" 2>nul
)

REM 3. 清除应用数据缓存
echo 📁 正在清除应用数据缓存...
set APPDATA_DIR=%APPDATA%\aipyapp
if exist "%APPDATA_DIR%" (
    echo    - 删除: %APPDATA_DIR%
    rmdir /s /q "%APPDATA_DIR%" 2>nul
)

set LOCALAPPDATA_DIR=%LOCALAPPDATA%\aipyapp
if exist "%LOCALAPPDATA_DIR%" (
    echo    - 删除: %LOCALAPPDATA_DIR%
    rmdir /s /q "%LOCALAPPDATA_DIR%" 2>nul
)

REM 4. 清除Electron应用缓存
echo ⚡ 正在清除Electron缓存...
set ELECTRON_CACHE=%APPDATA%\AI助手
if exist "%ELECTRON_CACHE%" (
    echo    - 删除: %ELECTRON_CACHE%
    rmdir /s /q "%ELECTRON_CACHE%" 2>nul
)

set ELECTRON_CACHE2=%LOCALAPPDATA%\AI助手
if exist "%ELECTRON_CACHE2%" (
    echo    - 删除: %ELECTRON_CACHE2%
    rmdir /s /q "%ELECTRON_CACHE2%" 2>nul
)

REM 5. 清除可能的数据库文件
echo 🗄️  正在清除数据库缓存...
set DB_LOCATIONS[0]=%USERPROFILE%\.aipyapp\cache.db
set DB_LOCATIONS[1]=%APPDATA%\aipyapp\cache.db
set DB_LOCATIONS[2]=%LOCALAPPDATA%\aipyapp\cache.db
set DB_LOCATIONS[3]=%USERPROFILE%\.aipyapp\providers.db
set DB_LOCATIONS[4]=%APPDATA%\aipyapp\providers.db

for /L %%i in (0,1,4) do (
    call set "db_file=%%DB_LOCATIONS[%%i]%%"
    if exist "!db_file!" (
        echo    - 删除: !db_file!
        del /f /q "!db_file!" 2>nul
    )
)

REM 6. 清除临时文件
echo 🧹 正在清除临时文件...
set TEMP_DIRS[0]=%TEMP%\aipyapp
set TEMP_DIRS[1]=%TEMP%\AI助手
set TEMP_DIRS[2]=%TMP%\aipyapp

for /L %%i in (0,1,2) do (
    call set "temp_dir=%%TEMP_DIRS[%%i]%%"
    if exist "!temp_dir!" (
        echo    - 删除: !temp_dir!
        rmdir /s /q "!temp_dir!" 2>nul
    )
)

REM 7. 清除浏览器缓存（如果软件使用内嵌浏览器）
echo 🌐 正在清除浏览器缓存...
set BROWSER_CACHE=%LOCALAPPDATA%\AI助手\User Data
if exist "%BROWSER_CACHE%" (
    echo    - 删除: %BROWSER_CACHE%
    rmdir /s /q "%BROWSER_CACHE%" 2>nul
)

REM 8. 清除工作目录缓存
echo 📂 正在清除工作目录缓存...
if exist "work" (
    echo    - 删除: work目录
    rmdir /s /q "work" 2>nul
)

echo.
echo ✅ 缓存清除完成！
echo.
echo 📝 清除的内容包括：
echo    ✓ 用户配置文件
echo    ✓ 应用数据缓存
echo    ✓ Electron缓存
echo    ✓ 数据库文件
echo    ✓ 临时文件
echo    ✓ 浏览器缓存
echo    ✓ 工作目录缓存
echo.
echo 🚀 现在可以重新启动软件了！
echo.
echo 按任意键退出...
pause >nul
