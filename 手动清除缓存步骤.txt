🔧 AI助手软件 - 手动清除缓存步骤

📋 问题描述:
软件显示错误的aihubmix API地址，需要清除缓存后重新启动。

🚀 自动清除方法（推荐）:

1. **运行清除脚本**
   - 双击运行 "清除软件缓存脚本.bat"
   - 等待脚本执行完成
   - 重新启动软件

🔧 手动清除方法:

**步骤1：完全关闭软件**
1. 关闭AI助手软件的所有窗口
2. 打开任务管理器（Ctrl+Shift+Esc）
3. 结束以下进程（如果存在）：
   - AI助手.exe
   - aipyapp.exe
   - electron.exe
   - 任何相关的Python进程

**步骤2：清除用户配置目录**
1. 按 Win+R 打开运行对话框
2. 输入：%USERPROFILE%
3. 查找并删除以下文件夹（如果存在）：
   - .aipyapp
   - .AI助手

**步骤3：清除应用数据目录**
1. 按 Win+R 打开运行对话框
2. 输入：%APPDATA%
3. 查找并删除以下文件夹（如果存在）：
   - aipyapp
   - AI助手

4. 按 Win+R 打开运行对话框
5. 输入：%LOCALAPPDATA%
6. 查找并删除以下文件夹（如果存在）：
   - aipyapp
   - AI助手

**步骤4：清除临时文件**
1. 按 Win+R 打开运行对话框
2. 输入：%TEMP%
3. 查找并删除以下文件夹（如果存在）：
   - aipyapp
   - AI助手

**步骤5：清除工作目录**
1. 在软件安装目录中查找 "work" 文件夹
2. 如果存在，删除整个 "work" 文件夹

**步骤6：清除数据库文件**
查找并删除以下可能的数据库文件：
- %USERPROFILE%\.aipyapp\cache.db
- %USERPROFILE%\.aipyapp\providers.db
- %APPDATA%\aipyapp\cache.db
- %APPDATA%\aipyapp\providers.db
- %LOCALAPPDATA%\aipyapp\cache.db

**步骤7：重新启动软件**
1. 重新启动AI助手软件
2. 进入设置 → 模型配置
3. 点击"添加"按钮
4. 选择"aihubmix"提供商
5. 验证API地址是否显示为：https://api.aihubmix.com/v1

🔍 验证清除效果:

**成功标志：**
✅ API地址字段显示：https://api.aihubmix.com/v1
✅ 软件启动时重新创建配置文件
✅ 提供商列表正常显示
✅ 添加功能正常工作

**如果仍然显示错误地址：**
❌ 可能需要检查软件版本是否为修复后的版本
❌ 可能需要重新安装软件

🛠️ 故障排除:

**问题1：无法删除某些文件夹**
解决方案：
- 确保软件完全关闭
- 以管理员身份运行清除脚本
- 重启电脑后再次尝试

**问题2：清除后软件无法启动**
解决方案：
- 检查软件安装是否完整
- 重新安装软件
- 检查防病毒软件是否误删文件

**问题3：API地址仍然错误**
解决方案：
- 确认使用的是修复后的软件版本
- 检查是否有其他配置文件覆盖
- 联系技术支持

📝 重要提醒:

⚠️ **备份重要数据**
清除缓存前，请备份以下重要数据：
- 自定义的API密钥配置
- 重要的对话记录
- 自定义设置

⚠️ **网络连接**
清除缓存后首次启动可能需要网络连接来重新下载必要的配置。

⚠️ **重新配置**
清除缓存后，您需要重新配置：
- API密钥
- 自定义提供商
- 个人偏好设置

🎯 预期结果:

清除缓存并重启后，您应该看到：
✅ aihubmix API地址正确显示为：https://api.aihubmix.com/v1
✅ 软件界面恢复正常
✅ 所有功能正常工作
✅ 不再出现502错误

📞 技术支持:

如果按照以上步骤操作后问题仍然存在，请提供：
1. 操作系统版本
2. 软件版本信息
3. 具体的错误截图
4. 是否完成了所有清除步骤
5. 清除后的软件行为描述
