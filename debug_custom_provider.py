#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义提供商调试脚本
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def start_debug_mode():
    """启动调试模式"""
    print_header("启动AI助手软件调试模式")
    
    try:
        # 启动调试模式
        print_info("正在启动AI助手软件调试模式...")
        print_info("请注意观察控制台输出中的[DEBUG]信息")
        print()
        
        # 创建调试启动脚本
        debug_script = '''
import subprocess
import sys
import os

# 启动AI助手软件并显示调试输出
try:
    # Windows下启动软件
    if os.name == 'nt':
        process = subprocess.Popen(
            ["AiPy.exe"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
    else:
        process = subprocess.Popen(
            ["./AiPy"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
    
    print("AI助手软件已启动，正在监控调试输出...")
    print("=" * 60)
    
    # 实时显示输出
    for line in iter(process.stdout.readline, ''):
        if line:
            print(line.rstrip())
            
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1)
'''
        
        with open("debug_start.py", "w", encoding="utf-8") as f:
            f.write(debug_script)
        
        print_success("调试启动脚本已创建: debug_start.py")
        print()
        print("🚀 现在请:")
        print("   1. 运行: python debug_start.py")
        print("   2. 或者直接启动 AiPy.exe 并观察控制台输出")
        print("   3. 按照测试步骤进行操作")
        print("   4. 观察[DEBUG]输出信息")
        
        return True
        
    except Exception as e:
        print_error(f"创建调试脚本失败: {e}")
        return False

def create_test_checklist():
    """创建测试检查清单"""
    print_header("创建测试检查清单")
    
    checklist = '''# 🧪 自定义提供商问题调试检查清单

## 📋 测试前准备

- [ ] 已重新打包应用程序 (asar pack extracted_app resources\\app.asar)
- [ ] 已完全重启AI助手软件
- [ ] 准备好有效的aihubmix API密钥
- [ ] 启用了调试模式或控制台输出

## 🔧 问题1：模型删除测试

### 测试步骤：
1. [ ] 添加一个测试用的自定义aihubmix模型
   - 名称：`Test-Delete-AIHUBMIX`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：任意测试密钥

2. [ ] 保存配置后，尝试删除该模型

3. [ ] 观察调试输出：
   - [ ] 看到 `[OFFLINE] Deleting provider locally: xxx`
   - [ ] 看到 `[OFFLINE] Found provider to delete: {...}`
   - [ ] 看到 `[OFFLINE] Provider deleted successfully`
   - [ ] 看到 `[OFFLINE] Related meta configs deleted`

### 预期结果：
- [ ] ✅ 模型从列表中完全消失
- [ ] ✅ 不再出现在模型选择中
- [ ] ✅ 没有删除错误信息

### 如果删除失败：
- [ ] 检查错误信息：`Provider xxx not found`
- [ ] 检查是否有权限问题
- [ ] 尝试重启软件后再删除

## 🔧 问题2：401认证错误测试

### 测试步骤：
1. [ ] 添加自定义aihubmix模型（使用真实API密钥）
   - 名称：`My-AIHUBMIX`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：您的真实密钥

2. [ ] 保存配置，确认提供商类型显示为"aihubmix"

3. [ ] 选择该模型，发送测试消息：`今天几月几号`

4. [ ] 观察调试输出：
   - [ ] 看到 `[DEBUG] Provider config being sent: {...}`
   - [ ] 看到 `[DEBUG] Custom config loaded: {...}`
   - [ ] 看到 `[DEBUG] Database record: {...}`
   - [ ] 检查配置中的api_key字段是否有值

### 预期结果：
- [ ] ✅ 不再出现401认证错误
- [ ] ✅ 能正常接收回复
- [ ] ✅ 提供商类型保持为"aihubmix"

### 如果仍有401错误：
- [ ] 检查调试输出中的api_key字段是否为空
- [ ] 检查customConfig是否正确加载
- [ ] 确认API密钥本身是否有效

## 🔧 问题3：提供商类型保持测试

### 测试步骤：
1. [ ] 添加自定义aihubmix模型
2. [ ] 保存后立即检查提供商类型

### 预期结果：
- [ ] ✅ 提供商类型显示为"aihubmix"（不是"openai"）

### 如果类型错误：
- [ ] 检查providerInfoList中的配置
- [ ] 确认修复是否正确应用

## 📊 调试信息收集

### 关键调试输出：
```
[OFFLINE] Creating provider locally: {...}
[OFFLINE] Provider created successfully with ID: xxx
[DEBUG] Provider config being sent: {...}
[DEBUG] Custom config loaded: {...}
[DEBUG] Database record: {...}
[OFFLINE] Deleting provider locally: xxx
[OFFLINE] Found provider to delete: {...}
[OFFLINE] Provider deleted successfully
```

### 需要检查的字段：
- [ ] customConfig.apiKey 是否有值
- [ ] customConfig.baseUrl 是否正确
- [ ] customConfig.type 是否为"aihubmix"
- [ ] 最终配置中的api_key字段是否正确

## 🎯 成功标准

### 完全成功：
- [ ] ✅ 能正常添加aihubmix自定义模型
- [ ] ✅ 提供商类型保持为"aihubmix"
- [ ] ✅ 能正常删除自定义模型
- [ ] ✅ 不再出现401认证错误
- [ ] ✅ 能正常发送接收消息

### 部分成功：
- [ ] 部分功能正常，记录具体问题

### 需要进一步修复：
- [ ] 记录具体错误信息和调试输出

## 📝 测试报告模板

**测试时间：** ___________

**删除功能测试：**
- 结果：[ ] 成功 [ ] 失败
- 错误信息：___________

**401认证测试：**
- 结果：[ ] 成功 [ ] 失败
- 错误信息：___________

**提供商类型测试：**
- 结果：[ ] 成功 [ ] 失败
- 显示类型：___________

**关键调试输出：**
```
（粘贴相关的[DEBUG]输出）
```

**总体评价：**
[ ] 完全成功 [ ] 部分成功 [ ] 需要进一步修复

**备注：**
___________
'''
    
    try:
        with open("自定义提供商调试检查清单.md", "w", encoding="utf-8") as f:
            f.write(checklist)
        print_success("调试检查清单已创建: 自定义提供商调试检查清单.md")
        return True
    except Exception as e:
        print_error(f"创建检查清单失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 自定义提供商调试助手")
    print("=" * 60)
    print("帮助您调试和测试自定义提供商的问题")
    print("=" * 60)
    
    results = []
    
    # 启动调试模式
    debug_ok = start_debug_mode()
    results.append(("调试模式启动", debug_ok))
    
    # 创建测试检查清单
    checklist_ok = create_test_checklist()
    results.append(("测试检查清单创建", checklist_ok))
    
    # 总结结果
    print_header("调试助手设置完成")
    
    all_passed = True
    for test_name, passed in results:
        if passed:
            print_success(f"{test_name}: 成功")
        else:
            print_error(f"{test_name}: 失败")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 调试环境准备完成！")
        print("\n🚀 现在请:")
        print("   1. 运行: python debug_start.py (启动调试模式)")
        print("   2. 查看: 自定义提供商调试检查清单.md")
        print("   3. 按照检查清单逐项测试")
        print("   4. 记录调试输出和测试结果")
        print("   5. 报告具体的问题和错误信息")
        
    else:
        print("❌ 调试环境设置失败，请检查错误信息")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 调试助手被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 调试助手运行错误: {e}")
        sys.exit(1)
