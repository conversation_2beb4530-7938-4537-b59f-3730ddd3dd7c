
🔧 AI助手软件 - API地址配置指南

📋 正确的API地址列表:

OpenAI:
   • API地址: https://api.openai.com/v1
   • 说明: OpenAI官方API地址
   • 支持模型: GPT-4, GPT-3.5-turbo等

Claude (Anthropic):
   • API地址: https://api.anthropic.com/v1
   • 说明: Anthropic官方API地址
   • 支持模型: Claude-3系列

aihubmix:
   • API地址: https://api.aihubmix.com/v1
   • 说明: aihubmix第三方服务商
   • 支持模型: 多种模型聚合服务

DeepSeek:
   • API地址: https://api.deepseek.com/v1
   • 说明: DeepSeek官方API地址
   • 支持模型: DeepSeek-Chat, DeepSeek-Coder

Moonshot (月之暗面):
   • API地址: https://api.moonshot.cn/v1
   • 说明: 月之暗面官方API地址
   • 支持模型: Moonshot系列

智谱AI:
   • API地址: https://open.bigmodel.cn/api/paas/v4
   • 说明: 智谱AI官方API地址
   • 支持模型: GLM-4系列

豆包 (字节跳动):
   • API地址: https://ark.cn-beijing.volces.com/api/v3
   • 说明: 字节跳动豆包API地址
   • 支持模型: 豆包系列

自定义提供商:
   • API地址: (用户自定义)
   • 说明: 支持任何OpenAI兼容的API服务
   • 格式要求: 必须包含完整的API路径

🔧 配置注意事项:

1. API地址格式:
   • 必须以 https:// 开头
   • 包含完整的API路径和版本号
   • 不要在末尾添加多余的斜杠

2. 常见错误:
   ❌ https://aihubmix.com (缺少api子域名和版本路径)
   ✅ https://api.aihubmix.com/v1 (正确格式)
   
   ❌ https://api.openai.com (缺少版本路径)
   ✅ https://api.openai.com/v1 (正确格式)

3. 验证机制:
   • 系统会自动验证API地址的有效性
   • 智能处理URL，避免重复添加版本路径
   • 支持不同提供商的API格式差异

🛠️ 故障排除:

API连接失败:
   • 检查API地址格式是否正确
   • 确认网络连接正常
   • 验证API密钥有效性
   • 检查提供商服务状态

地址显示错误:
   • 重启软件重新加载配置
   • 检查是否为最新修复版本
   • 手动修正错误的API地址

验证失败:
   • 确认API密钥格式正确
   • 检查API服务是否正常
   • 尝试使用"跳过验证"选项

💡 使用建议:

1. 优先使用官方API地址:
   • 稳定性更好
   • 功能支持更完整
   • 安全性更高

2. 第三方服务商选择:
   • 选择信誉良好的服务商
   • 注意API限制和计费方式
   • 定期检查服务可用性

3. 自定义配置:
   • 确保API兼容OpenAI格式
   • 测试所有必要的端点
   • 备份重要配置

📞 技术支持:

如果遇到API地址相关问题：
1. 检查软件版本是否为最新修复版
2. 确认API地址格式符合要求
3. 测试网络连接和API服务状态
4. 查看软件日志获取详细错误信息
5. 联系技术支持获取帮助

🎉 总结:

经过API地址修复，AI助手软件现在支持：
• 正确的aihubmix API地址
• 所有主流提供商的标准API格式
• 智能的URL验证和处理机制
• 兼容各种API版本路径格式

这些修复确保了用户能够正确配置和使用各种LLM提供商！
    