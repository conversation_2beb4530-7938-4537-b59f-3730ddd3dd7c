"use strict";const u=require("fs"),p=require("path"),se=require("bunyan"),d=require("electron"),k=require("os"),ie=require("https");require("crypto");const G=require("child_process"),ne=require("node-machine-id"),q=require("@electron-toolkit/utils"),w=require("intl-template"),re=require("he"),W=require("pkce-challenge"),oe=require("undici"),h=require("zod/v4"),J=require("node:sqlite"),ae=require("@std/toml"),v=require("node:fs"),M=require("node:path"),le=require("lodash"),ce=require("stream"),g=require("electron-updater"),de=require("adm-zip"),pe=require("@modelcontextprotocol/sdk/client/index.js"),ue=require("@modelcontextprotocol/sdk/client/streamableHttp.js"),he=require("node-pty"),O=p.join(k.homedir(),".aipyapp"),D=p.join(k.homedir(),"aipywork"),_=!d.app.isPackaged&&process.env.ELECTRON_RENDERER_URL?p.join(__dirname,"../..","resources"):p.join(process.resourcesPath,"app.asar.unpacked/resources"),R=p.join(d.app.getPath("userData"),"python/site-packages"),fe=process.platform==="win32"?p.join(_,"python/Lib/site-packages"):p.join(_,"python/lib/python3.12/site-packages"),Q=[fe,R,process.env.PYTHONPATH].filter(Boolean).join(p.delimiter);u.existsSync(R)||u.mkdirSync(R,{recursive:!0});let Z=(()=>{let a=process.env[k.platform()==="win32"?"COMSPEC":"SHELL"];return a||k.platform()==="win32"||(a=k.userInfo().shell,a)?a:k.platform()==="darwin"?"/bin/zsh":null})();const me=`Mozilla/5.0 (${process.platform}; ${process.arch}) AppleWebKit/537.36 (KHTML, like Gecko) aipy-pro/${d.app.getVersion()} Chrome/134.0.6998.179 Safari/537.36`;let V="";try{V=ne.machineIdSync()}catch(a){try{V=Object.values(k.networkInterfaces()).flat().filter(e=>e&&e.mac&&e.internal===!1).map(e=>e.mac).sort()[0]||""}catch{}$({message:"Failed to get machine ID",error:a})}const P={"User-Agent":me,"X-AIPY-VERSION":d.app.getVersion(),"X-AIPY-PLATFORM":process.platform,"X-AIPY-ARCH":process.arch,"X-AIPY-MACHINE-ID":V,"X-AIPY-UID":"","X-AIPY-USERNAME":""};class Te{TIMESTAMP_BITS=42;WORKER_ID_BITS=10;SEQUENCE_BITS=12;lastTimestamp=-1;sequence=0;maxSequence=(1<<this.SEQUENCE_BITS)-1;constructor(e=0,t=17468352e5){if(e<0||e>31)throw new Error("Worker ID must be between 0 and 31");this.workerId=e,this.epoch=t}generate(){let e=Date.now();if(e<this.lastTimestamp)throw new Error("Clock is moving backwards!");e===this.lastTimestamp?(this.sequence=this.sequence+1&this.maxSequence,this.sequence===0&&(e=this.tilNextMillis(e))):this.sequence=0,this.lastTimestamp=e;const s=(e-this.epoch).toString(2).padStart(this.TIMESTAMP_BITS,"0"),i=this.workerId.toString(2).padStart(this.WORKER_ID_BITS,"0"),n=this.sequence.toString(2).padStart(this.SEQUENCE_BITS,"0"),r=`${s}${i}${n}`;return BigInt(`0b${r}`).toString(34).toString()}tilNextMillis(e){let t=Date.now();for(;t<=e;)t=Date.now();return t}}const U=new Te(0);let A={};function Ee(){if(A.PATH)return A;const a=k.homedir();if(!a)throw new Error('Could not determine user"s home directory.');let e=process.env.SHELL,t,s;k.platform()==="win32"?(e=process.env.COMSPEC||"cmd.exe",s="set",t=["/c",s]):(e||(e="/bin/bash"),s="env",t=["-ilc",s]);const n=G.spawnSync(e,t,{cwd:a,detached:!0,stdio:["ignore","pipe","pipe"],shell:!1,encoding:"utf-8"});if(n.status!==0){const o=new Error(`Shell command failed with exit code ${n.status}`);throw o.code=n.status,o.stdout=n.stdout.toString(),o.stderr=n.stderr.toString(),o}return A={},n.output.toString("utf-8").split(`
`).forEach(o=>{const c=o.trim();if(c){const l=c.indexOf("=");if(l>0){const T=c.substring(0,l),y=c.substring(l+1);A[T]=y}}}),A.PATH=A.Path||A.PATH||"",A}const C=async(a,e={})=>{const t=new Headers({...P,...e.headers||{}});return!t.get("content-type")&&typeof e.body=="object"&&(t.set("Content-Type","application/json"),e.body=JSON.stringify(e.body)),globalThis.fetch(a,{...e,headers:t}).then(async s=>{const i=s.headers.get("content-type")||"";let n;if(i.toLowerCase().includes("application/json")?n=await s.json():i.includes("text")&&(n=await s.text()),!s.ok||n?.status===!1||n?.success===!1){const r=new Error(`[${s.status}]${typeof n=="string"&&n||n?.message||n?.msg||"Fetch failed"}`);throw r.status=s.status,r.statusText=s.statusText,r.url=a,r.headers=s.headers,r.payload=e.body,r.response=n,r}return{body:n,status:s.status,statusText:s.statusText,headers:s.headers,time:Date.now()}})};function H(a,e,t={}){const s=new Set;Object.getOwnPropertyNames(e).forEach(i=>s.add(i)),Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(i=>s.add(i)),s.forEach(i=>{if(i==="constructor")return;const n=e[i];d.ipcMain.handle(`${a}:${i}`,async(r,...o)=>{if(typeof n!="function")return e[i];const c=performance.now();let l;try{l=await n(...o)}catch(y){throw console.error({args:o,elapsed:performance.now()-c,error:y},`${a}:${i}`),y}const T=t[i];return T!==!1&&console.info({args:o,ret:T===null?"":l,elapsed:performance.now()-c},`${a}:${i}`),l})})}function ye(a,e){const t=p.dirname(e);return u.existsSync(t)||u.mkdirSync(t,{recursive:!0}),new Promise((s,i)=>{console.debug({url:a,dest:e,baseDir:t},"downloadFromUrl");const n=u.createWriteStream(e);ie.get(a,r=>{r.pipe(n),n.on("finish",()=>{n.close(s)})}).on("error",r=>{console.error(r,"download file from url failed"),require("fs").unlink(e,()=>i(r))})})}function K(a,e=1e3){return a.split("-")[0].split(".").map(Number).reverse().reduce((s,i,n)=>s+i*Math.pow(e,n),0)}if(!u.existsSync(p.join(O,"log")))try{u.mkdirSync(p.join(O,"log"),{recursive:!0})}catch(a){process.stderr.write(`Failed to create log directory: ${a.message}
`)}const B=se.createLogger({name:d.app.getName(),streams:[{type:"rotating-file",path:p.join(O,"log/aipy-pro.log"),period:"3y",count:3},{level:d.app.isPackaged?"error":"debug",stream:process.stdout}]});setTimeout(()=>{const a=B.streams.find(e=>e.type==="rotating-file");if(a){a.stream.rotAt=0;try{a.stream.rotate()}catch(e){B.error({msg:"Failed to rotate log file on startup",error:e})}}});function $({type:a="node",message:e,error:t,...s}){console.log({type:a,message:e,error:t,...s},"report")
// 离线模式：禁用错误上报到官方服务器
// d.app.isPackaged&&fetch(`https://www.aipyaipy.com/e/collect?${new URLSearchParams({type:a,message:e,error:t?.stack?.split(`
// `).slice(0,2).join(`
// `)})}`,{method:"POST",headers:P,body:JSON.stringify({type:a,message:e,error:t?t.stack||t.message:void 0,detail:s})})
}process.on("unhandledRejection",a=>{$({message:"unhandledRejection",error:a})});process.on("uncaughtException",a=>{$({message:"uncaughtException",error:a})});globalThis.console=new Proxy(B,{get(a,e){switch(e){case"log":return B.info.bind(B);case"report":return $}return a[e]}});const ge=p.join(__dirname,"./chunks/icon-BEbKvBha.ico"),m={SYSTEM:"SYSTEM",STATE:"STATE",LLM:"LLM",CODE:"CODE",STDIN:"STDIN",USER:"USER",TOOL:"TOOL"},ee={TEXT:"TEXT",STATUS:"STATUS"},I={INITIALING:"INITIALING",RUNNING:"RUNNING",IDLE:"IDLE",EXIT:"EXIT"},X={general:h.z.looseObject({workdir:h.z.string().optional(),lang:h.z.string().default("zh").optional(),role:h.z.string().default("aipy").optional(),max_rounds:h.z.number().int().min(1).max(64).optional(),timeout:h.z.number().int().min(0).max(120).optional(),auto_download_update:h.z.boolean().optional(),share_result:h.z.boolean().default(!0)}),llm:h.z.record(h.z.string(),h.z.object({type:h.z.string().optional(),model:h.z.string().optional(),base_url:h.z.url().optional(),models_endpoint:h.z.string().optional(),api_key:h.z.string().optional(),max_tokens:h.z.number().int().min(1).max(1024*1024).optional(),default:h.z.boolean().optional(),enable:h.z.boolean().default(!0),models:h.z.array(h.z.string()).optional()})),mcp:h.z.object({mcpServers:h.z.record(h.z.string(),h.z.union([h.z.object({command:h.z.string(),args:h.z.array(h.z.string()).optional(),env:h.z.record(h.z.string(),h.z.string()).optional(),enabled:h.z.boolean().default(!1),disabled:h.z.boolean().optional()}),h.z.object({url:h.z.url(),transport:h.z.object({type:h.z.enum(["streamable_http","sse","stdio"])}).optional(),headers:h.z.looseObject().optional(),enabled:h.z.boolean().default(!1),disabled:h.z.boolean().optional()})]))}),runtime:h.z.object({npm_registry:h.z.url(),pip_index:h.z.url()})};function N(a,e=()=>"key"){let t={};return function(...s){const i=e(...s);let n=t[i];return n||(n=Promise.resolve(a(...s)),n.finally(()=>{t[i]=null}),t[i]=n,n)}}const we=[{version:"0.6.0",async apply({db:a}){if(!a.prepare("SELECT value FROM setting WHERE category = 'general' AND field = 'lang' LIMIT 1;").get()){const e=M.join(O,"aipyapp.toml");if(v.existsSync(e))try{const t=v.readFileSync(e,"utf-8"),s=await ae.parse(t),i=a.prepare("INSERT OR IGNORE INTO setting (category, field, value) VALUES (?, ?, ?)");for(const n in s){let r=s[n];switch(n){case"lang":(r==="zh"||r==="en")&&i.run("general",n,r);break;case"workdir":v.statSync(r,{throwIfNoEntry:!1})?.isDirectory?.()&&i.run("general",n,r);break;case"max_rounds":case"timeout":r=parseInt(r,10),!isNaN(r)&&r>=0&&i.run("general",n,r.toString());break;case"auto_download_update":case"share_result":i.run("general",n,r===!0?"true":"false");break}}}catch(t){console.error("[LOAD-SETTINGS]","aipyapp.toml",t)}}}},{version:"0.6.3",async apply({db:a}){const e=a.prepare("SELECT value FROM meta WHERE field = ? LIMIT 1;").get(f.TRUSTOKEN_MODELS);if(!e)return;const t=JSON.parse(e.value);t.length&&!t[0]?.id&&a.prepare("INSERT OR REPLACE INTO meta (field, value) VALUES (?, ?)").run(f.TRUSTOKEN_MODELS,JSON.stringify(t.map(s=>({id:s}))))}}];async function Se(a,e){const t=d.app.getVersion(),s=K(t),i=parseInt(a.prepare("SELECT value FROM meta WHERE field = 'version'").get()?.value??s,10);i>=s&&!t.includes("alpha")&&console.log("No migrations needed"),console.log(`Migration: ${i} -> ${s}`);const n={db:a,settings:e,prevVersion:i,currentVersion:s};for(const r of we){const o=K(r.version);if((t.includes("alpha")?o>=i:o>i)&&o<=s){console.log(`Applying migration: ${r.version}`);try{await r.apply(n)}catch(c){throw console.error(`Failed to apply migration ${r.version}:`,c),c}}}a.prepare("INSERT OR REPLACE INTO meta (field, value) VALUES ('version', ?)").run(s.toString()),Re()}function Re(){const a="3.12.11";if(v.existsSync(R)){let e="";v.existsSync(M.join(R,"runtime-version"))&&(e=v.readFileSync(M.join(R,"runtime-version"),"utf-8").trim()),e!==a&&setTimeout(()=>{console.log("Removing old pip target files...",R);try{for(const t of v.readdirSync(R))console.log(`Removing old pip target file: ${t}`),v.rmSync(M.join(R,t),{recursive:!0,force:!0});v.writeFileSync(M.join(R,"runtime-version"),a,"utf-8")}catch(t){console.error("Failed to remove old pip target files:",t)}},0)}}const f={TRUSTOKEN_TOKEN:"trustoken-token",TRUSTOKEN_API:"trustoken-api",TRUSTOKEN_KEY:"trustoken-key",TRUSTOKEN_MODELS:"trustoken-models",TRUSTOKEN_PROVIDER_MIGRATED:"trustoken-provider-migrated",TRUSTOKEN_PROFILE:"trustoken-profile",TRUSTOKEN_CUSTOM_PROVIDER:"trustoken-custom-provider",TRUSTOKEN_PROVIDER_INFO:"trustoken-provider-info",AIPY_ROLES:"aipy-roles",SUGGEST_INSTRUCTIONS:"suggest-instuctions",EXTENSIONS:"extensions"};class be extends J.DatabaseSync{stmt={};init=async()=>{this.initTable(),await Se(this),this.initStmt(),this.initData()};initTable=()=>{this.exec(`
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA busy_timeout = 5000;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 1000000;

CREATE TABLE IF NOT EXISTS task (
	id TEXT PRIMARY KEY
	, title TEXT
	, instruction TEXT
	, system_prompt TEXT
	, provider TEXT
	, provider_id INTEGER
	, model TEXT
	, role TEXT DEFAULT ''
	, tools TEXT DEFAULT '[]'
	, workdir TEXT DEFAULT ''
	, create_time INTEGER
) STRICT;

CREATE TABLE IF NOT EXISTS task_event (
	id TEXT PRIMARY KEY
	, task_id TEXT REFERENCES task(id) ON DELETE CASCADE
	, cid TEXT
	, provider TEXT
	, model TEXT
	, type TEXT
	, content TEXT
	, time INTEGER
) STRICT;

CREATE TABLE IF NOT EXISTS provider (
	id INTEGER PRIMARY KEY
	, enable INTEGER DEFAULT 1
	, name TEXT
	, type TEXT
	, channel_type INTEGER
	, is_custom INTEGER DEFAULT 1
	, model TEXT
	, max_tokens INTEGER DEFAULT 8192
) STRICT;

CREATE TABLE IF NOT EXISTS setting (
	category TEXT
	, field TEXT
	, value TEXT DEFAULT ''
	, PRIMARY KEY (category, field)
) STRICT;

CREATE TABLE IF NOT EXISTS meta (
	field TEXT PRIMARY KEY
	, value TEXT DEFAULT ''
) STRICT;

CREATE TABLE IF NOT EXISTS attachment (
	type TEXT
	, path TEXT
	, sha256 TEXT
	, PRIMARY KEY (type, path)
) STRICT;

CREATE TABLE IF NOT EXISTS extension (
		id TEXT PRIMARY KEY
		, path TEXT
		, name TEXT
		, display_name TEXT
		, version TEXT
		, description TEXT
		, author TEXT DEFAULT '{}'
		, icon TEXT
		, keywords TEXT DEFAULT '[]'
		, enabled INTEGER DEFAULT 1
)
		`);const e=this.prepare("PRAGMA table_info('task');").all().reduce((s,i)=>(s[i.name]=i,s),{});e?.systemPrompt&&this.exec(`
				ALTER TABLE task RENAME COLUMN systemPrompt TO system_prompt;
			`),e?.settings&&(this.exec(`
				BEGIN TRANSACTION;
				ALTER TABLE task DROP COLUMN settings;
				ALTER TABLE task ADD COLUMN workdir TEXT DEFAULT '';
				ALTER TABLE task ADD COLUMN tools TEXT default '[]';
				COMMIT;
			`),this.prepare("UPDATE task SET workdir = ? WHERE workdir = '';").run(D)),e?.model||this.exec("ALTER TABLE task ADD COLUMN model TEXT;"),e?.provider||this.exec("ALTER TABLE task ADD COLUMN provider TEXT;"),e?.provider_id||(this.exec("ALTER TABLE task ADD COLUMN provider_id INTEGER;"),this.exec("UPDATE task SET provider_id = 0, model = 'auto', provider = 'trustoken' WHERE 1 = 1;")),e?.role||(this.exec("ALTER TABLE task ADD COLUMN role TEXT DEFAULT '';"),this.exec("UPDATE task SET role = 'aipy' WHERE role = '';")),this.prepare("PRAGMA table_info('task_event');").all().reduce((s,i)=>(s[i.name]=i,s),{})?.taskId&&this.exec(`
				ALTER TABLE task_event RENAME COLUMN taskId TO task_id;
			`),this.exec(`
			CREATE INDEX IF NOT EXISTS idx_task_event_task_id ON task_event (task_id);
		`)};initStmt=()=>{this.stmt=Object.freeze({taskInsertOne:this.prepare(`
				INSERT INTO task (id, workdir, provider, provider_id, model, title, instruction, role, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			`),getTaskList:this.prepare(`
				SELECT
					id
					, title
					, instruction
					, system_prompt as systemPrompt
					, workdir
					, tools
					, provider
					, provider_id as providerId
					, model
					, role
					, create_time as createTime
				FROM task
				ORDER BY create_time DESC
			`),deleteTask:this.prepare("DELETE FROM task WHERE id = ?"),getTask:this.prepare(`
				SELECT
					id
					, title
					, instruction
					, system_prompt as systemPrompt
					, workdir
					, provider
					, provider_id as providerId
					, model
					, tools
					, role
					, create_time as createTime
				FROM task
				WHERE id = ?
				LIMIT 1
			`),taskEventInsertOne:this.prepare(`
				INSERT OR REPLACE INTO task_event (
					id
					, task_id
					, cid
					, provider
					, model
					, type
					, content
					, time
				) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
			`),getTaskEventList:this.prepare(`
				SELECT
					id
					, task_id as taskId
					, cid
					, provider
					, model
					, type
					, content
					, time
				FROM task_event
				WHERE task_id = ?
				ORDER BY time ASC
			`),settingFindByCategory:this.prepare("SELECT category, field, value FROM setting WHERE category = ?"),settingUpsert:this.prepare("INSERT OR REPLACE INTO setting (category, field, value) VALUES (?, ?, ?)"),metaFindByField:this.prepare("SELECT field, value FROM meta WHERE field = ? LIMIT 1"),metaUpsertByField:this.prepare("INSERT OR REPLACE INTO meta (field, value) VALUES (?, ?)"),metaInsertRecordByField:this.prepare("INSERT OR IGNORE INTO meta (field, value) VALUES (?, ?)"),providerFindAll:this.prepare(`
				SELECT
				  id
					, enable
					, name
					, type
					, channel_type as channelType
					, is_custom as isCustom
					, model
					, max_tokens as maxTokens
				FROM provider
			`),providerFindById:this.prepare(`
				SELECT
				  id
					, enable
					, name
					, type
					, channel_type as channelType
					, is_custom as isCustom
					, model
					, max_tokens as maxTokens
				FROM provider
				WHERE id = ?
			`),providerUpdateById:this.prepare(`
				UPDATE provider
				SET
					name = ?
					, type = ?
					, channel_type = ?
					, model = ?
					, max_tokens = ?
				WHERE id = ?
			`),providerInsertOne:this.prepare(`
				INSERT OR REPLACE INTO provider (
					id
					, enable
					, name
					, type
					, channel_type
					, is_custom
					, model
					, max_tokens
				)
				VALUES (?, ?, ?, ?, ?, ?, ?, ?)
			`),providerDeleteById:this.prepare("DELETE FROM provider WHERE id = ?"),attachmentInsertOne:this.prepare(`
				INSERT OR REPLACE INTO attachment (type, path, sha256) VALUES (?, ?, ?)
			`)})};initData=()=>{this.prepare(`
			INSERT OR IGNORE INTO setting (category, field, value) VALUES
				('runtime', 'npm_registry', ?)
				, ('runtime', 'pip_index', ?);
		`).run(d.app.getLocale().includes("zh")?"https://registry.npmmirror.com/":"https://registry.npmjs.org/",d.app.getLocale().includes("zh")?"https://mirrors.aliyun.com/pypi/simple/":"https://pypi.org/simple/"),this.prepare("INSERT OR IGNORE INTO provider (id, name, type, is_custom, model, max_tokens) VALUES (?, ?, ?, ?, ?, ?)").run(0,"trustoken","trust",0,"auto",8192);const e={zh:[{instruction:"分析当前电脑CPU、内存占用TOP10软件有哪些？"},{instruction:"写本1000字短篇小说《我的AI女友》，保存为HTML在当前目录下，保存好后，使用电脑默认浏览器打开它。"},{instruction:"分析我电脑安装了哪些软件，借此推判我的职业和爱好。"}],en:[{instruction:"Analyze the top 10 software consuming CPU and memory on my computer."},{instruction:'Write a 1000-word short story titled "My AI Girlfriend", save it as HTML in the current directory, and open it in the default web browser.'},{instruction:"Analyze the software installed on my computer to infer my profession and hobbies."}]};this.stmt.metaInsertRecordByField.run("suggest-instuctions",JSON.stringify(e));const t=[["51cd9ddf640b59277172cafa3c1b0c87852e25ec18d9f4bc937125d877d87044","3c_sales.xlsx"],["8f66030553cec14b9c494a1cc4f50d179331ba073fbc0b84d316f649bf136c18","attnd/2024年10月打卡记录.csv"],["daefedb386e8bcacbe72c642ec2ab2c7c34d16522d32b0072ad8250a084a27ec","attnd/2024年11月打卡记录.csv"],["acb9768952e711353a0b4243be6e3522cb8e102aeaa0da730676e53214e4ee54","attnd/2024年12月打卡记录.csv"],["065e3be968a6339a3456f2ce5e58ad0b3fca535252d854d35bf351363c1fa700","attnd/2024年1月打卡记录.csv"],["19e5a49ddc26ef2f0a9175b1b0d15ab209a0356f502f74a3aeb032e8d3bdc22c","attnd/2024年2月打卡记录.csv"],["08f6bf0b3211e00f767582b255909810658f0f8172ebacd6f3d0467b0ad5dc41","attnd/2024年3月打卡记录.csv"],["ab84b6234c86d6786002e2cd16c298ec1adef99d19280d679c65eea1b612a733","attnd/2024年4月打卡记录.csv"],["58e76c5dcd49042806bf3d27b992cf4570ba8ba478d0d6e72520bb48e7285500","attnd/2024年5月打卡记录.csv"],["adbc125d463d9f66bcb55b12ecf40e63a1f121ffd3bf1ece0576598039076278","attnd/2024年6月打卡记录.csv"],["943e97114ca79e1c7105da2e40036070881c4b6a324a0c9f533890c7de3984bf","attnd/2024年7月打卡记录.csv"],["eee9faed441cb3bcc72b92105cd494dee727a1bf3dd003aa97aa52e958bd8bfc","attnd/2024年8月打卡记录.csv"],["e2755a76622de62f0000264d773121d7bdc07a59c8a285cd74e136212c4f623d","attnd/2024年9月打卡记录.csv"],["bb08976b6dd4925f90f4fac97c4876c192355d3adedf889ba742c0c56df83826","edu_exam.xlsx"],["0d51a2caa34bfb4612e521e3d30a2d0bf5873eada84affbfaf8204922cb909eb","hospital_records.xlsx"],["071eb44e31e37046a5263be998e34b3e5a92742c4597f378200fd59f264f52af","nginx.log"],["c93ade0386432b55d2495869aadc1b7c995c9680de90d472c8c51e4074b1ac41","pc_lists.db"],["2f8efe6a672cd1bd947b96108b8561b463a63508c540b4689410b0f9b87d15a8","pic1.png"],["c413269a35de60339bf748d7391ab3470dc6cd91a825a0455f9261552c5f2c82","pic2.png"],["ffbbe5e6ba91dab97b895bbfcc20fdcd2154129ba26f35faed880cdde1b1aeb2","pic3.png"],["76c14f1ec21a17e046db6259c365c7289376438ba9c31ce11330385ea4b652f0","pic4.png"],["9b4d97ebe584a4f5a3b612c4449e686e91a8bfe79bd7efe7c2bb900eaea4f26e","rpts/FS_BZ25Q2.pdf"],["a4c7d6968e60058674f31793711fc8b55107d7abac1e9eb50fa8f18546afdd3f","rpts/FS_TSLA25Q2.pdf"],["392c41f134d82703cd62ca9019b3b4ec3045f9b3fa165839c57e446940391973","rpts/FS_VG25Q2.pdf"]];for(const[s,i]of t)this.stmt.attachmentInsertOne.run("demo",i,s)};ttToken=()=>{try{const e=this.stmt.metaFindByField.get(f.TRUSTOKEN_TOKEN);if(!e)throw new Error("TrustToken token not found");const t=JSON.parse(e?.value||"{}");if(!t.access_token)throw new Error(`TrustToken token not found. ${JSON.stringify(t)}`);return t}catch(e){throw console.error("[getTrustokenToken]",e),new Error("Failed to get TrustToken token",{cause:e})}};providerMigrated=()=>!!this.stmt.metaFindByField.get(f.TRUSTOKEN_PROVIDER_MIGRATED)?.value}const b={trustoken:{cn:{api:"https://api.trustoken.cn",www:"https://www.trustoken.cn"},ai:{api:"https://sapi.trustoken.ai",www:"https://www.trustoken.ai"}},aipy:{com:{www:"https://www.aipyaipy.com",store:"https://store.aipyaipy.com",download:"https://dl.aipyaipy.com/downloads"},app:{www:"https://www.aipy.app",store:"https://store.aipy.app",download:"https://dl.aipy.app/downloads"}}};class ke extends ce.EventEmitter{IS_LOADED=!1;LOAD_ERRORS=[];endpoints={tt:b.trustoken.cn,aipy:b.aipy.com};general={lang:d.app.getLocale().includes("zh")?"zh":"en",workdir:D,role:"aipy"};llm={};mcp={};constructor(e){if(super(),u.existsSync(p.join(O,"config"))||u.mkdirSync(p.join(O,"config"),{recursive:!0}),e.stmt.settingFindByCategory.all("general").reduce((t,s)=>{let i;switch(s.field){case"workdir":u.statSync(s.value,{throwIfNoEntry:!1})?.isDirectory()&&(t.workdir=s.value);break;case"lang":(s.value==="zh"||s.value==="en")&&(t.lang=s.value);break;case"max_rounds":case"timeout":i=parseInt(s.value,10),!isNaN(i)&&i>=0&&(t[s.field]=i);break;case"role":t[s.field]=s.value;break;case"auto_download_update":case"share_result":t[s.field]=s.value==="true";break}return t},this.general),this.loadEndpoints(),p.isAbsolute(this.general.workdir)||(this.general.workdir=p.join(k.homedir(),this.general.workdir)),this.general.workdir||(this.general.workdir=D),!u.existsSync(this.general.workdir))try{u.mkdirSync(this.general.workdir,{recursive:!0})}catch(t){console.error(`[LOAD-SETTINGS] Failed to create work directory: ${this.general.workdir}, fallback to ${D}`,t),this.general.workdir=D}}load=async()=>{
// 首先加载TOML配置文件中的LLM设置
const defaultTomlPath = p.join(_, "python/Lib/site-packages/aipyapp/res/default.toml");
if(u.existsSync(defaultTomlPath)) {
  try {
    const tomlContent = u.readFileSync(defaultTomlPath, "utf-8");
    const tomlData = ae.parse(tomlContent);
    if(tomlData.llm) {
      console.log("[LOAD-SETTINGS] Loading LLM config from default.toml");
      // 将TOML中的LLM配置转换为正确的格式
      for(const [name, config] of Object.entries(tomlData.llm)) {
        if(config.enable !== false) {
          this.llm[name] = {
            type: config.type,
            base_url: config.api_base,
            api_key: config.api_key,
            enable: config.enable !== false,
            model: config.model || "auto",
            max_tokens: config.max_tokens || 8192
          };
          console.log(`[LOAD-SETTINGS] Loaded LLM provider from TOML: ${name}`);
        }
      }
    }
  } catch(n) {
    console.error("[LOAD-SETTINGS] Error loading default.toml:", n);
  }
}

const e=p.join(O,"config/llm.json");if(u.existsSync(e)){let s="",i={};try{s=u.readFileSync(e,"utf-8"),i=await JSON.parse(s),Object.assign(this.llm,X.llm.parse(i))}catch(n){console.error("[LOAD-SETTINGS]",n)}}const t=p.join(O,"mcp.json");if(u.existsSync(t)){let s="",i={};try{s=u.readFileSync(t,"utf-8"),i=await JSON.parse(s),Object.assign(this.mcp,X.mcp.parse(i))}catch(n){console.error("[LOAD-SETTINGS]",n)}}return this.loadMcpToolsCache(),this.IS_LOADED=!0,this.emit("load",this),this};update=async e=>{for(const t in e)switch(t){case"general":return this.updateGeneral(e[t]);case"mcp":try{const s=Object.entries(e[t]?.mcpServers||{}).reduce((i,[n,r])=>(r?.type==="sys"||(i[n]=r),i),{});this.mcp=X.mcp.parse({mcpServers:s})}catch(s){throw new Error(s)}u.writeFileSync(p.join(O,"mcp.json"),JSON.stringify(this.mcp,null,4),"utf-8"),this.loadMcpToolsCache();break}};updateGeneral=async e=>{if(e.workdir&&!u.existsSync(e.workdir))throw new Error(w.l10n`Work directory does not exist: ${e.workdir}`.toString());try{X.general.parse(e)}catch(s){throw new Error(s)}const t=globalThis.db.stmt.settingUpsert;for(const[s,i]of Object.entries(e))i!==this.general[s]&&t.run("general",s,i.toString());this.general={...this.general,...e}};loadMcpToolsCache=()=>{const e=p.join(O,"cache.db");if(u.existsSync(e))try{const s=new J.DatabaseSync(e).prepare("SELECT key, value FROM cache").all().reduce((i,n)=>{const[r,o,c]=n.key.split(":");if(r!=="mcp_tool"||!o||!c)return i;const l=JSON.parse(n.value||"{}");return i[o]=l,i},{});Object.entries(s).forEach(([i,n])=>{n&&n.length>0&&(i.startsWith("Trustoken-")?this.mcp.mcpServers[i]={type:"sys",tools:n}:this.mcp.mcpServers[i]&&(this.mcp.mcpServers[i].tools=n))})}catch{}};loadEndpoints=()=>{let e=d.app.getLocale().includes("zh")?"zh":"en";const t=globalThis.db.stmt.metaFindByField.get(f.TRUSTOKEN_API);t&&(e=t?.value?.includes?.("trustoken.cn")?"zh":"en"),e==="zh"?this.endpoints={tt:b.trustoken.cn,aipy:b.aipy.com}:this.endpoints={tt:b.trustoken.ai,aipy:b.aipy.app}};waitForLoaded=()=>new Promise(e=>{const t=()=>{this.IS_LOADED?e(this):setTimeout(t,10)};t()})}class Oe{settings={};result=null;constructor({window:e,settings:t,url:s}){this.settings=t;let n=d.app.getVersion().split("-")[1]?.split(".")[0];return["latest","beta","alpha","rc"].includes(n)||(n="latest"),g.autoUpdater.autoDownload=t.general.auto_download_update,g.autoUpdater.autoInstallOnAppQuit=!1,g.autoUpdater.forceDevUpdateConfig=!d.app.isPackaged,g.autoUpdater.setFeedURL({url:s,channel:n,requestHeaders:P,provider:"generic"}),g.autoUpdater.requestHeaders=P,g.autoUpdater.on("error",r=>{console.error("[CHECK-UPDATE]",r)}),g.autoUpdater.on("download-progress",r=>{console.log("[UPDATE-PROGRESS]",r),e.webContents.send("aipy:update-progress",r)}),g.autoUpdater.on("update-downloaded",r=>{console.log("[UPDATE-DOWNLOADED]",r),d.dialog.showMessageBox({type:"info",title:w.l10n`AiPy Update`.toString(),message:w.l10n`AiPy updates ${r.version} downloaded, Quit and update now?`.toString(),buttons:process.platform==="win32"?["NO","YES"]:[w.l10n`Cancel`.toString(),w.l10n`OK`.toString()]}).then(o=>{o.response===1&&g.autoUpdater.quitAndInstall()})}),g.autoUpdater.on("update-available",r=>{console.log("[UPDATE-AVAILABLE]",r)}),g.autoUpdater.on("checking-for-update",r=>{console.log("[CHECKING-FOR-UPDATE]",r)}),new Proxy(g.autoUpdater,{get:(r,o)=>o in this?this[o]:r[o]})}isCheckingForUpdates=!1;checkForUpdates=async(e=!1)=>{if(this.isCheckingForUpdates)return await new Promise(t=>{const s=setInterval(()=>{this.isCheckingForUpdates||(clearInterval(s),t())},10)}),{isUpdateAvailable:this.result?.isUpdateAvailable,currentVersion:d.app.getVersion(),updateInfo:this.result?.updateInfo};if(!e&&this.result)return{isUpdateAvailable:this.result?.isUpdateAvailable,currentVersion:d.app.getVersion(),updateInfo:this.result?.updateInfo};this.result=null,this.isCheckingForUpdates=!0;try{const t=await g.autoUpdater.checkForUpdates();return t&&(this.result=t),{isUpdateAvailable:this.result?.isUpdateAvailable,currentVersion:d.app.getVersion(),updateInfo:this.result?.updateInfo}}catch(t){throw console.error("[CHECK-UPDATE]",t),t}finally{this.isCheckingForUpdates=!1}};download=()=>{if(this.result?.isUpdateAvailable)return g.autoUpdater.autoDownload=!1,g.autoUpdater.downloadUpdate()}}const F={redirect_uri:"aipy-pro://oauth",scope:"all",client_id:"aipy-pro"};class j{constructor(e,t){return this.taskId=e,this.configDir=t,this.stdin=!1,this.state="",this.destroyed=!1,this.controller=new AbortController,this.process=null,this.port=0,this.workdir="",this.model="auto",this.provider={},this.role="",this.listener={event:[],close:[]},new Proxy(this,{get:(s,i)=>{const n=s[i];return n!==void 0?n:(i.match(/[A-Z]/)&&(i=i.replace(/[A-Z]/g,r=>`-${r.toLowerCase()}`)),r=>this.request(`/${i}`,r?{headers:{"content-type":"application/json"},method:"POST",body:JSON.stringify(r)}:{method:"GET"}))}})}static spawn(e=[],t={}){let s="python/bin/python";process.platform==="win32"&&(s="python\\python.exe");let i={};if(process.platform==="darwin")try{i=Ee()}catch(l){console.error(l,"getLoginShellEnvironment")}const n={...process.env,...i,PIP_TARGET:R,PYTHONPATH:Q,PYTHONHOME:p.join(_,"python"),PYTHONIOENCODING:"UTF-8"},r=process.platform==="win32"?!0:Z;let o=[p.join("aipy-stdio","main.py")].concat(e);const c=_;return console.log({command:s,args:o,options:t,shell:r,env:n,cwd:c},"spawn"),G.spawn(s,o,{cwd:c,stdio:"pipe",encoding:"utf8",shell:r,windowsHide:!0,env:n,...t})}spawn(e=[]){let t=["--task-id",this.taskId].concat(e);try{this.process=j.spawn(t,{signal:this.controller.signal}),this.process.stderr.on("data",this.handleStdout),this.process.stdout.on("data",s=>{this.destroyed!==!0&&console.log({content:s.toString("utf8")},"stdout")}),this.process.on("close",this.handleExit),this.process.on("disconnect",(...s)=>{console.log({args:s},"process:disconnect"),this.handleExit()}),this.process.on("error",(...s)=>{console.log({args:s},"process:error"),this.handleError(`Process error: ${s[0]}`),this.handleExit()}),this.process.on("exit",(...s)=>{console.log({args:s},"process:exit"),this.handleExit()})}catch(s){console.error(s,"process:spawn"),this.handleError(s.message),this.handleExit()}}on(e,t){this.listener[e].push(t)}get isRunning(){return this.process?.stdin&&!this.process?.stdin?.closed&&!this.process?.stdin?.destroyed}input=e=>{if(!this.isRunning)throw console.debug({taskId:this.taskId},"process:input:destroyed"),new Error("Process is not running.");this.process.stdin.write(`${JSON.stringify({instruction:e})}
`)};request=async(e,t={method:"GET"})=>{if(!this.isRunning||!this.port)return console.warn({taskId:this.taskId,path:e,options:t},"skip-request"),null;const{connectTimeout:s=6e5,headersTimeout:i=3e5,bodyTimeout:n=3e5,...r}=t,o=`http://127.0.0.1:${this.port}${e}`;return globalThis.fetch(o,{...r,dispatcher:new oe.Agent({connectTimeout:s,headersTimeout:i,bodyTimeout:n})}).then(async c=>{if(!c.ok)return console.error("request error",c.status,o,t,await c.text()),new Error(`${e} ${c.status}`);let l;return(c.headers.get("content-type")||"").toLocaleLowerCase().includes("json")?l=await c.json():l=await c.text(),console.debug({taskId:this.taskId,status:c.status,path:e,options:t,responseBody:l?`
[RESPONSE]${JSON.stringify(l)}`:""},"request"),l})};requestExit=()=>this.request("/exit").catch(()=>{}).finally(()=>{this.isRunning&&(this.process.stdin.destroy(),this.process.kill()),this.handleExit()});providerChange=e=>{const t={name:"",max_tokens:8192,model:"auto",timeout:void 0,api_key:"",stream:!0,params:{},temperature:.6,...e};return this.request("/provider",{method:"POST",headers:{"content-type":"application/json"},body:JSON.stringify(t)}).then(()=>{this.provider=t})};handleExit=()=>{this.state=I.EXIT,this.port=0,this.model="",this.provider={},this.workdir="",this.listener.event.forEach(e=>e({id:U.generate(),taskId:this.taskId,type:m.STATE,content:{type:I.EXIT},time:Date.now()})),this.listener.event=[],this.listener.close.forEach(e=>e()),this.listener.close=[]};stdoutBuf="";handleStdout=e=>{if(this.destroyed===!0){console.debug("[process:handleStdout:destroyed]",JSON.stringify({taskId:this.taskId,data:e.toString("utf8")})),this.process.kill();return}const t=e.toString("utf8"),s=(this.stdoutBuf+t).split(`
`);if(s.length===1){this.stdoutBuf+=t;return}this.stdoutBuf=s.pop(),this.stdoutBuf&&console.log({last:this.stdoutBuf},"process:stdoutBuf");const i=s.reduce((n,r)=>{if(r=r.trim(),!r)return n;let o;try{o=JSON.parse(r)}catch{return console.warn({line:r},"stderr"),n}if(!o?.type)return console.warn({event:o},"invalid-event"),n;if(o.id=U.generate(),o.taskId=this.taskId,o.time=Date.now(),o.type===m.STATE){const c=o.content;c.type===I.INITIALING&&c.port&&(this.port=o.content.port,this.role=o.content.role),this.state=c.type}else o.type===m.STDIN&&(this.stdin=o.content);return n.push(o),n},[]);i.length!==0&&i.forEach(n=>{this.listener.event.forEach(r=>r(n))})};handleError=e=>{const t={type:m.SYSTEM,content:{type:ee.TEXT,value:e},id:U.generate(),taskId:this.taskId,time:Date.now()};this.listener.event.forEach(s=>s(t))};waitStdinOpen=()=>new Promise(e=>{const t=setInterval(()=>{(this.stdin&&this.port&&this.state===I.IDLE||!this.isRunning)&&(clearInterval(t),e())},Math.ceil(10*Math.random()))});waitHttpStart=()=>new Promise(e=>{const t=setInterval(()=>{(this.port&&(this.state===I.IDLE||this.state===I.RUNNING)||!this.isRunning)&&(clearInterval(t),e())},Math.ceil(10*Math.random()))})}const Ie=a=>!a||!Array.isArray(a)?(console.error("校验数据不是数组"),a):a.map(t=>{const{facets:s,...i}=t;if(!s||!Array.isArray(s))return t;const n=t.instruction;return s.forEach(r=>{if(r.type!=="demo-file")return;const o=p.join(_,"demo"),c=p.join.apply(null,r.value.split(/(@?<demo>)/).map(l=>l.includes("<demo>")?l.replace("<demo>",o):l.split(/\/|\\/).join(p.sep)));i.instruction=i.instruction.replace(r.value,c)}),{...i,facets:s,raw:n}});class _e{#e=null;#n="";#t={};#s=null;#i=null;#l=null;constructor(e,t,s){this.#e=e,this.#i=t,this.#s=s,this.#n=O;const{stdout:i,stderr:n}=G.spawnSync(process.platform==="win32"?"python.exe":"bin/python",["-c",JSON.stringify("print(__import__('aipyapp').__version__)")],{encoding:"utf8",shell:!0,env:{PYTHONIOENCODING:"UTF-8"},cwd:p.join(_,"python")});n&&console.error("[aipyappVersion]",i,n),i&&(this.aipyappVersion=i.trim()),this.#l=new Oe({settings:s,window:t,url:s.endpoints.aipy.download}),this.getSettingsRuntime()}#E=e=>{if(!e.type){console.log("[EVENT-DROP]",JSON.stringify(e));return}let t=e.content;(e.type===m.SYSTEM||e.type===m.STDIN||e.type===m.STATE||e.type===m.CODE||e.type===m.TOOL)&&(t=JSON.stringify(e.content));try{return this.#e.stmt.taskEventInsertOne.run(e.id,e.taskId,e.cid||"",e.provider||"",e.model||"",e.type,t,e.time)}catch(s){return console.error({...e,content:t,error:s},"EVENT-INSERT-ERROR"),null}};#c=e=>{if(!this.#i||this.#i?.isDestroyed?.()){console.debug("[sendEvent:destroyed]",JSON.stringify({isDestroyed:this.#i?.isDestroyed?.(),event:e}));return}try{const t=JSON.stringify(e);console.log(e,"event"),this.#i.webContents.send("aipy:event",t)}catch(t){console.error(t,"send user input failed")}};#h=e=>{this.#c(e),e.type!==m.STDIN&&e.type!==m.STATE&&e.done!==!1&&this.#E(e)};input=(e,t)=>{this.#t?.[e]?.input?.(t);const s={taskId:e,id:U.generate(),type:m.USER,content:t,time:Date.now()};return this.#c(s),this.#e.stmt.taskEventInsertOne.run(s.id,s.taskId,null,null,null,s.type,s.content,s.time)};stop=()=>{Object.values(this.#t).forEach(e=>{e.isRunning&&e.requestExit()})};updateSettings=async e=>{await this.#s.update(e),e?.mcp&&await this.mcpListTools()};settingsMcpDeleteOne=async e=>{const t={...this.#s.mcp.mcpServers};delete t[e],await this.#s.update({mcp:{...this.#s.mcp,mcpServers:t}}),Object.values(this.#t).map(s=>{s.isRunning&&s.reloadSettings()})};getSettingsRuntime=()=>{const e=this.#e.stmt.settingFindByCategory.all("runtime").reduce((t,s)=>(t[s.field]=s.value,t),{});return this.#f(e),e};updateSettingsRuntime=e=>{Object.entries(e).forEach(([t,s])=>{this.#e.stmt.settingUpsert.run("runtime",t,s)}),this.#f(e)};#f(e){Object.entries(e).forEach(([t,s])=>{switch(t){case"npm_registry":process.env.NPM_CONFIG_REGISTRY=s;break;case"pip_index":process.env.PIP_FIND_LINKS=s,process.env.PIP_INDEX_URL=s,process.env.UV_INDEX=s,process.env.UV_NATIVE_TLS="true";break}})}settingsRestore=e=>{const t=new Date().toLocaleString("swe").replace(/\D/g,"");e.forEach(s=>{u.existsSync(s)&&s.startsWith(this.#n)&&u.renameSync(s,`${s}.${t}.bak`)}),d.app.relaunch(),d.app.exit(0)};createTask=async({title:e,instruction:t,mcp:s})=>{const i=U.generate(),n=new j(i,this.#n);n.on("event",this.#h);const r=p.join(this.#s.general.workdir,i);return u.existsSync(r)||u.mkdirSync(r,{recursive:!0}),this.#c({taskId:i,id:U.generate(),type:m.STATE,content:{type:I.INITIALING,title:e||"",instruction:t||""},time:Date.now()}),this.#a?.base_url||await this.ttProviderSync(),n.spawn(["--config",JSON.stringify(JSON.stringify({provider:this.#a,lang:this.#s.general.lang,workdir:r,max_rounds:this.#s.general.max_rounds,timeout:this.#s.general.timeout,share_result:this.#s.general.share_result}))]),this.#t[i]=n,this.#e.stmt.taskInsertOne.run(i,r,"trustoken",0,this.#a?.model||"auto",e||null,t||null,this.#s.general.role,Date.now()),n.waitStdinOpen().then(()=>{Promise.allSettled([this.loadRole(i,this.#s.general.role),s?this.mcpToggle(s.reduce((o,c)=>(o[c]=!0,o),{})):Promise.resolve()]).then(()=>{t&&this.input(i,t)})}),i};getTaskList=()=>this.#e.stmt.getTaskList.all().map(e=>({...e,state:this.#t?.[e.id]?.state||I.EXIT,tools:JSON.parse(e.tools||"[]")}));deleteTask=async e=>{if(this.#t[e]){if(this.#t[e].isRunning)try{await this.#t[e].requestExit()}catch(t){console.error("[deleteTask:requestExit]",t)}delete this.#t[e]}this.#e.stmt.deleteTask.run(e)};cancelTask=e=>{const t=this.#t?.[e];if(t)return t.cancel()};#m=(e,t,s)=>{let i=s||this.getTaskById(e);const n=u.readFileSync(p.join(_,"preview.html")).toString();u.writeFileSync(t||p.join(i.workdir,"task.html"),n.replace("<!--DATA-->",re.escape(JSON.stringify(i))))};taskHtmlExport=async e=>{const t=this.getTaskById(e);if(!t)throw new Error(`Task ${e} not found`);const s=(t.title||t.instruction||t.id).slice(0,30).replaceAll(p.sep,"_"),{canceld:i,filePath:n}=await d.dialog.showSaveDialog({defaultPath:p.join(t.workdir,`${s}.html`)});!i&&n&&this.#m(e,n,t)};stopTask=async e=>(setTimeout(()=>this.#m(e)),this.#t?.[e]?.requestExit());getTaskById=e=>{const t=this.#e.stmt.getTask.get(e);if(t){const s=this.getTaskEventList(e);return{...t,state:this.#t?.[e]?.state||I.EXIT,stdin:this.#t?.[e]?.stdin||!1,events:s,tools:JSON.parse(t.tools||"[]")}}return null};taskWorkdirById=e=>this.#e.stmt.getTask.get(e)?.workdir;resumeTask=N(async(e,{title:t,instruction:s,mcp:i})=>{const n=this.getTaskById(e);if(!n)throw new Error(`Task ${e} not found`);if(this.#t[e]&&!this.#t[e].isRunning&&(console.log({taskId:e,isRunning:this.#t[e]?.isRunning,destroyed:this.#t[e]?.destroyed},"resumeTask:clear process"),this.#t[e].requestExit(),delete this.#t[e]),this.#c({taskId:e,id:U.generate(),type:m.STATE,content:{type:I.INITIALING,title:t||"",instruction:s||""},time:Date.now()}),!this.#t[e]?.isRunning){console.log({taskId:e,isRunning:this.#t[e]?.isRunning,destroyed:this.#t[e]?.destroyed},"resumeTask-create process");const r=new j(e,this.#n);r.on("event",this.#h);let o=n.workdir;(!o||!u.existsSync(o))&&(o=p.join(this.#s.general.workdir,n.id),u.existsSync(o)||u.mkdirSync(o,{recursive:!0})),this.#a?.base_url||await this.ttProviderSync(),r.spawn(["--config",JSON.stringify(JSON.stringify({lang:this.#s.general.lang,provider:this.#a,workdir:o,max_rounds:this.#s.general.max_rounds,timeout:this.#s.general.timeout,share_result:this.#s.general.share_result}))]),this.#t[e]=r}return await this.#t[e].waitHttpStart(),n.model&&await this.taskUpdateProviderById(e,{provider:n.provider,providerId:n.providerId,model:n.model}),n.role&&await this.loadRole(e,n.role),i&&await this.mcpToggle(e,i.reduce((r,o)=>(r[o]=!0,r),{})),t&&this.#e.prepare("UPDATE task SET title = ? WHERE id = ? AND title IS NULL").run(t,e),s&&(this.#e.prepare("UPDATE task SET instruction = ? WHERE id = ? AND instruction IS NULL").run(s,e),await this.input(e,s)),await this.#t[e].load(n.events?.reduce((r,o)=>((o.type===m.LLM||o.type===m.USER)&&r.push({role:o.type===m.LLM?"assistant":"user",content:o.content}),r),[])),e},e=>e);taskUpdateInstructionById=(e,t)=>{this.#e.prepare("UPDATE task SET title = ?, instruction = ? WHERE id = ?").run(t,t,e)};taskUpdateProviderById=async(e,{provider:t,providerId:s,model:i})=>{if(!(!e||s===void 0||!i)){if(this.#t?.[e]?.isRunning){let n={...this.#a,model:i,name:t,provider_id:-1,timeout:this.#s.general.timeout>0?this.#s.general.timeout:void 0};if(s>0){const r=this.#e.stmt.providerFindById.get(s);// 尝试从meta表加载完整的自定义提供商配置
        const customConfigMeta = this.#e.prepare("SELECT value FROM meta WHERE field = ?").get(`custom_provider_${s}_config`);
        let customConfig = {};
        if (customConfigMeta) {
          try {
            customConfig = JSON.parse(customConfigMeta.value);
          } catch (e) {
            console.warn("[DEBUG] Failed to parse custom provider config:", e);
          }
        }
        
        n={
          ...n,
          max_tokens:r.maxTokens||1024,
          base_url: customConfig.baseUrl || r.baseUrl || "",
          baseUrl: customConfig.baseUrl || r.baseUrl || "",
          api_key: customConfig.apiKey || r.apiKey || "",
          api_base: customConfig.baseUrl || r.baseUrl || "",
          type: customConfig.type || r.type || "openai"
        }

        console.log("[DEBUG] Custom config loaded:", JSON.stringify(customConfig, null, 2));
        console.log("[DEBUG] Database record:", JSON.stringify(r, null, 2));}else s<0&&(n={...this.#s.llm[t]||{},name:t,provider_id:-1,model:i});console.log("[DEBUG] Provider config being sent:", JSON.stringify(n, null, 2));
      await this.#t[e].providerChange(n)}this.#e.prepare("UPDATE task SET provider = ?, provider_id = ?, model = ? WHERE id = ?").run(t,s,i,e)}};getTaskEventList=e=>{let t=this.#e.stmt.getTaskEventList.all(e).reduce((i,n)=>{let r=n.content;return(n.type===m.SYSTEM||n.type===m.STDIN||n.type===m.STATE||n.type===m.CODE||n.type===m.TOOL)&&(r=JSON.parse(n.content)),i.push({...n,content:r}),i},[]);const s=this.#t?.[e];return s?.isRunning?t=t.concat([{taskId:e,type:m.STATE,content:s.state},{taskId:e,type:m.STDIN,content:s.stdin}]):t=t.filter(i=>!(i.type===m.SYSTEM&&i.content?.type===ee.STATUS)).concat([{taskId:e,type:m.STATE,content:{type:I.EXIT}},{taskId:e,type:m.STDIN,content:!1}]),t};mcpStatus=async e=>this.#t?.[e]?.isRunning?(await this.#t[e].waitHttpStart(),this.#t?.[e]?.mcpStatus()):Object.entries(this.#s.mcp?.mcpServers||{}).reduce((t,[s,i])=>(i?.type==="sys"?t.sys[s]={enabled:i.enabled,...i}:i?.tools?.length>0&&(t.user[s]={enabled:i.enabled}),t),{user:{},sys:{}});mcpToggle=async(e,t)=>{if(!e)return;let s=this.#t?.[e];return s?.isRunning||(await this.resumeTask(e,{}),s=this.#t?.[e]),await s.waitHttpStart(),s.mcpToggle(t)};mcpListTools=()=>new Promise((e,t)=>{try{this.mcpListToolsProcess=j.spawn(["--mcp-list-tools","--config",JSON.stringify(JSON.stringify({provider:this.#a}))]),this.mcpListToolsProcess.stderr.on("data",s=>{!this.#i||this.#i.isDestroyed()||(console.log("[mcp-list-tools][stderr]",s.toString("utf8")),this.#i.webContents.send("aipy:mcp-list-tools",s.toString("utf8")))}),this.mcpListToolsProcess.stdout.on("data",s=>{!this.#i||this.#i.isDestroyed()||(console.log("[mcp-list-tools][stdout]",s.toString("utf8")),this.#i.webContents.send("aipy:mcp-list-tools",s.toString("utf8")))}),this.mcpListToolsProcess.on("close",s=>{this.mcpListToolsProcess=null,t(s)}),this.mcpListToolsProcess.on("error",s=>{!this.#i||this.#i.isDestroyed()||(console.log("[mcp-list-tools][error]",s),this.mcpListToolsProcess=null,this.#i.webContents.send("aipy:mcp-list-tools",`
${s.message}
`),t(s))}),this.mcpListToolsProcess.on("exit",()=>{this.mcpListToolsProcess=null,e(),this.#s.loadMcpToolsCache()})}catch(s){this.mcpListTools&&this.#i.webContents.send("aipy:mcp-list-tools",`
${s.message}
`),console.error("[mcpListTools]",s),t(s)}});mcpListToolsStop=()=>{this.mcpListToolsProcess&&(this.mcpListToolsProcess.kill(),this.mcpListToolsProcess=null)};providerCustomList=x(f.TRUSTOKEN_CUSTOM_PROVIDER,async()=>{
// 离线模式：返回本地数据库中的自定义提供商
console.log("[OFFLINE] Loading custom providers from local database");
try {
  // 从数据库获取自定义提供商
  const providers = this.#e.stmt.providerFindAll.all()
    .filter(p => p.is_custom === 1)
    .map(p => ({
      id: p.id,
      name: p.name,
      type: p.type,
      apiKey: "", // 离线模式下不显示API密钥
      channelType: p.channel_type,
      baseUrl: "",
      models: []
    }));

  console.log("[OFFLINE] Found custom providers:", providers.length);
  return providers;
} catch(e) {
  console.error("[OFFLINE] Error loading custom providers:", e);
  return [];
}
// const e=await this.#o("/api/user/custom-models/conf").then(t=>(t||[]).map(({supplier_id:s,supplier:i,key:n,...r})=>({...r,apiKey:n,channelType:s,type:this.#d[s]?.sdk,baseUrl:i?.base_url})));return await this.providerInfoList(),e.forEach(({id:t,channelType:s})=>{const i=this.#e.stmt.providerFindById.get(t),n=this.#d[s]?.name||t.toString();this.#e.prepare(`INSERT OR REPLACE INTO provider (id, enable, name, type, channel_type, is_custom, model, max_tokens) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`).run(t,i?.enable||1,i?.channelType===s?i?.name:n,i?.type||"openai",s,1,i?.model||"",i?.maxTokens||1024)}),e.length>0?this.#e.prepare(`DELETE FROM provider WHERE is_custom = 1 AND id NOT IN (${e.map(()=>"?").join(",")})`).run(...e.map(t=>JSON.stringify(t.id))):this.#e.exec("DELETE FROM provider WHERE is_custom = 1"),e
});providerList=async(e={})=>{const t=this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_API),s=this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_MODELS),i=await this.providerCustomList(e).catch(()=>[]);let n={trustoken:{type:"trust",name:"trustoken",baseUrl:t?.value?`${t.value}/api`:"",models:s?.value?JSON.parse(s.value):[],isCustom:!1},custom:i.map(({id:o,type:c,name:l,apiKey:T,models:y,channelType:E,baseUrl:L})=>({name:l,id:o,models:y,type:c,apiKey:T,baseUrl:L,isCustom:!0,channelType:E,originalId:o}))};n=this.#e.stmt.providerFindAll.all().reduce((o,c)=>{let l;return c.type==="trust"&&!c.isCustom?(l=o.trustoken,l.id=c.id):(l=o.custom.find(T=>T.originalId===c.id||T.name===c.name),l||(l={id:c.id,type:c.type,name:c.name||c.id||"",isCustom:1,channelType:c.channel_type||0,originalId:c.id},o.custom.push(l))),l&&(l.enable=!!c.enable,l.model=c.model,l.maxTokens=c.maxTokens,c.name&&(l.name=c.name)),o},n);const r=n.custom.reduce((o,c)=>(o[c.name]=c,o),{});return n.local=Object.entries(this.#s.llm||{}).reduce((o,[c,l])=>(c.toLowerCase()==="trustoken"||l.base_url&&l.base_url.startsWith(t?.value)||(r[c] ?
          o.push({name:`Local-${c}`,id:-1,enable:l.enable!==!1,type:l.type,baseUrl:l.base_url,models:l.models||[],isCustom:!0,channelType:-1,apiKey:l.api_key||"",model:l.model||"auto",maxTokens:l.max_tokens||4096}) :
          o.push({name:c,id:-1,enable:l.enable!==!1,type:l.type,baseUrl:l.base_url,models:l.models||[],isCustom:!0,channelType:-1,apiKey:l.api_key||"",model:l.model||"auto",maxTokens:l.max_tokens||4096})),o),[]),n};providerCreateOne=async({name:e,channelType:t,apiKey:s,model:i,models:n=[],maxTokens:r,skipValidation:skipVal=false,baseUrl:baseUrlParam})=>{
// 离线模式：在本地数据库创建提供商，支持API验证
console.log("[OFFLINE] Creating provider locally:", {name: e, channelType: t, skipValidation: skipVal});
try {
  // 获取提供商信息
  const providerInfo = await this.providerInfoList();
  const supplier = providerInfo.find(p => p.id === t) || {sdk: "openai", baseUrl: ""};

  // 确定API基础URL
  const baseUrl = baseUrlParam || supplier.baseUrl || "";

  // 如果提供了API密钥和基础URL，进行验证
  if (s && baseUrl && !skipVal) {
    console.log("[OFFLINE] Validating API key before creating provider");
    const validation = await this.providerValidateApiKey({
      baseUrl: baseUrl,
      apiKey: s,
      skipValidation: skipVal
    });

    if (!validation.valid) {
      console.error("[OFFLINE] API validation failed:", validation.message);
      throw new Error(`API validation failed: ${validation.message}`);
    }

    console.log("[OFFLINE] API validation successful");

    // 如果验证返回了模型列表，使用它们
    if (validation.models && validation.models.length > 0) {
      n = validation.models.map(m => m.id || m.model || m);
    }
  }

  // 生成一个新的ID
  const newId = Date.now();

  // 插入到数据库
  // 保持用户选择的提供商类型，不使用supplier.sdk
  const providerType = e.toLowerCase().includes("aihubmix") ? "aihubmix" : 
                      e.toLowerCase().includes("claude") ? "anthropic" :
                      e.toLowerCase().includes("deepseek") ? "deepseek" :
                      e.toLowerCase().includes("moonshot") ? "moonshot" :
                      e.toLowerCase().includes("zhipu") ? "zhipu" :
                      e.toLowerCase().includes("doubao") ? "doubao" :
                      e.toLowerCase().includes("ollama") ? "ollama" :
                      supplier.sdk || "openai";
  
  // 保存完整的提供商配置，包括API密钥和基础URL
  this.#e.stmt.providerInsertOne.run(newId, 1, e, providerType, t, 1, i || "auto", r || 8192);
  
  // 额外保存API密钥和基础URL到meta表，用于后续使用
  this.#e.prepare("INSERT OR REPLACE INTO meta (field, value) VALUES (?, ?)").run(
    `custom_provider_${newId}_config`, 
    JSON.stringify({
      apiKey: s,
      baseUrl: baseUrl,
      name: e,
      type: providerType,
      channelType: t
    })
  );

  console.log("[OFFLINE] Provider created successfully with ID:", newId);
  return {id: newId, name: e, supplier: supplier, models: n};
} catch(error) {
  console.error("[OFFLINE] Error creating provider:", error);
  throw error;
}
// const o=await this.#o("/api/user/custom-models/conf",{method:"POST",body:{key:s,models:n,supplier_id:t}});return this.#e.stmt.providerInsertOne.run(o.id,1,e,o?.supplier?.sdk||"openai",t,1,i,r),o
};providerUpdateById=async({name:e,type:t,id:s,channelType:i,apiKey:n,model:r,models:o=[],maxTokens:c,isCustom:l,skipValidation:skipVal=false,baseUrl:baseUrlParam})=>{
// 离线模式：更新本地数据库，支持API验证
console.log("[OFFLINE] Updating provider locally:", {id: s, name: e, skipValidation: skipVal});
try {
  // 获取提供商信息
  const providerInfo = await this.providerInfoList();
  const sdk = this.#d[i]?.sdk || providerInfo.find(p => p.id === i)?.sdk || "openai";

  if(l) {
    // 自定义提供商，如果提供了API密钥，进行验证
    console.log("[OFFLINE] Updating custom provider in local database");

    if (n && baseUrlParam && !skipVal) {
      console.log("[OFFLINE] Validating API key before updating provider");
      const validation = await this.providerValidateApiKey({
        baseUrl: baseUrlParam,
        apiKey: n,
        skipValidation: skipVal
      });

      if (!validation.valid) {
        console.error("[OFFLINE] API validation failed:", validation.message);
        throw new Error(`API validation failed: ${validation.message}`);
      }

      console.log("[OFFLINE] API validation successful");

      // 如果验证返回了模型列表，使用它们
      if (validation.models && validation.models.length > 0) {
        o = validation.models.map(m => m.id || m.model || m);
      }
    }
  } else {
    // 内置提供商，更新配置
    Object.assign(this.#a, {model: r});
  }

  // 更新数据库
  this.#e.stmt.providerUpdateById.run(e, t || sdk, i || 0, r, c, l ? s : 0);

  console.log("[OFFLINE] Provider updated successfully");
  return {models: o};
} catch(error) {
  console.error("[OFFLINE] Error updating provider:", error);
  throw error;
}
// if(l){const T=await this.#o(`/api/user/custom-models/conf/${s}`,{method:"PUT",body:{key:n,models:o,supplier_id:i}});console.log({payload:{key:n,models:o,supplier_id:i},resp:T})}else Object.assign(this.#a,{model:r});this.#e.stmt.providerUpdateById.run(e,t||this.#d[i]?.sdk||"openai",i||0,r,c,l?s:0)
};providerToggle=async(e,t)=>{
// 修复：支持所有提供商的开关切换，不仅限于自定义提供商
console.log("[OFFLINE] Toggling provider:", {id: e, enabled: t});
try {
  // 更新提供商的启用状态，支持自定义和内置提供商
  const result = this.#e.prepare("UPDATE provider SET enable = ? WHERE id = ?").run(t ? "1" : "0", e);
  console.log("[OFFLINE] Provider toggle result:", result);

  if (result.changes === 0) {
    console.warn("[OFFLINE] No provider found with ID:", e);
    throw new Error(`Provider with ID ${e} not found`);
  }

  console.log("[OFFLINE] Provider toggle successful");
  return result;
} catch(error) {
  console.error("[OFFLINE] Error toggling provider:", error);
  throw error;
}
};providerDeleteById=async e=>{
// 离线模式：删除本地数据库记录，支持按名称或ID删除
console.log("[OFFLINE] Deleting provider locally:", e);
try {
  // 首先查找要删除的提供商信息
  let providerToDelete = null;

  // 尝试按ID查找（支持字符串和数字ID）
  const numericId = parseInt(e);
  if (!isNaN(numericId)) {
    providerToDelete = this.#e.prepare("SELECT * FROM provider WHERE id = ? AND is_custom = 1").get(numericId);
    console.log("[OFFLINE] Searched by numeric ID:", numericId, "Found:", providerToDelete);
  }

  // 如果按ID没找到，尝试按名称查找
  if (!providerToDelete) {
    providerToDelete = this.#e.prepare("SELECT * FROM provider WHERE name = ? AND is_custom = 1").get(e);
    console.log("[OFFLINE] Searched by name:", e, "Found:", providerToDelete);
  }

  if (!providerToDelete) {
    console.warn("[OFFLINE] No custom provider found to delete:", e);
    // 列出所有自定义提供商用于调试
    const allCustom = this.#e.prepare("SELECT * FROM provider WHERE is_custom = 1").all();
    console.log("[OFFLINE] All custom providers:", allCustom);
    throw new Error(`Custom provider ${e} not found`);
  }

  console.log("[OFFLINE] Found provider to delete:", providerToDelete);

  // 删除provider记录
  let result = this.#e.prepare("DELETE FROM provider WHERE id = ? AND is_custom = 1").run(providerToDelete.id);

  if (result.changes > 0) {
    console.log("[OFFLINE] Provider deleted successfully, changes:", result.changes);

    // 删除meta表中的相关配置
    try {
      // 按ID删除配置
      this.#e.prepare("DELETE FROM meta WHERE field = ?").run(`custom_provider_${providerToDelete.id}_config`);
      // 按名称删除配置（模糊匹配）
      this.#e.prepare("DELETE FROM meta WHERE field LIKE ?").run(`%${providerToDelete.name}%`);
      console.log("[OFFLINE] Related meta configs deleted for provider:", providerToDelete.name);
    } catch(metaError) {
      console.warn("[OFFLINE] Failed to delete meta configs:", metaError);
    }
  } else {
    console.warn("[OFFLINE] Failed to delete provider from database");
    throw new Error(`Failed to delete provider ${e}`);
  }
} catch(error) {
  console.error("[OFFLINE] Error deleting provider:", error);
  throw error;
}
// await this.#o(`/api/user/custom-models/conf/${e}`,{method:"DELETE"}),this.#e.stmt.providerDeleteById.run(e)
};#d={};providerInfoList=x(f.TRUSTOKEN_PROVIDER_INFO,async()=>{
// 离线模式：返回常用的第三方LLM提供商信息
console.log("[OFFLINE] Returning default provider info list");
const defaultProviders = [
  {id: 1, name: "OpenAI", sdk: "openai", type: "openai", baseUrl: "https://api.openai.com/v1"},
  {id: 2, name: "Claude", sdk: "anthropic", type: "anthropic", baseUrl: "https://api.anthropic.com/v1"},
  {id: 3, name: "aihubmix", sdk: "aihubmix", type: "aihubmix", baseUrl: "https://api.aihubmix.com/v1"},
  {id: 4, name: "DeepSeek", sdk: "openai", type: "openai", baseUrl: "https://api.deepseek.com/v1"},
  {id: 5, name: "Moonshot", sdk: "openai", type: "openai", baseUrl: "https://api.moonshot.cn/v1"},
  {id: 6, name: "Zhipu", sdk: "openai", type: "openai", baseUrl: "https://open.bigmodel.cn/api/paas/v4"},
  {id: 7, name: "Doubao", sdk: "openai", type: "openai", baseUrl: "https://ark.cn-beijing.volces.com/api/v3"},
  {id: 8, name: "Custom", sdk: "openai", type: "openai", baseUrl: ""}
];
return defaultProviders;
// this.#o("/api/supplier").then(e=>(e||[]).map(({base_url:t,id:s,name:i,sdk:n,type:r})=>({id:s,name:i,sdk:n,type:r,baseUrl:t})))
});providerListModel=async({baseUrl:e,apiKey:t})=>{
// 离线模式：返回常用模型列表
console.log("[OFFLINE] Returning default model list for baseUrl:", e);
const defaultModels = [];

// 根据baseUrl返回对应的默认模型
if (e.includes("openai.com")) {
  defaultModels.push("gpt-4", "gpt-4-turbo", "gpt-3.5-turbo");
} else if (e.includes("anthropic.com")) {
  defaultModels.push("claude-3-opus", "claude-3-sonnet", "claude-3-haiku");
} else if (e.includes("deepseek.com")) {
  defaultModels.push("deepseek-chat", "deepseek-coder");
} else if (e.includes("moonshot.cn")) {
  defaultModels.push("moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k");
} else if (e.includes("bigmodel.cn")) {
  defaultModels.push("glm-4", "glm-3-turbo");
} else {
  // 通用模型列表
  defaultModels.push("gpt-3.5-turbo", "gpt-4", "claude-3-sonnet");
}

console.log("[OFFLINE] Returning models:", defaultModels);
return defaultModels;
// this.#o("/api/user/custom-models/models",{method:"POST",body:{base_url:e,key:t}})
};

// 新增：API密钥验证方法
providerValidateApiKey=async({baseUrl:e,apiKey:t,skipValidation:s=false})=>{
console.log("[API_VALIDATION] Validating API key for:", e);

// 如果跳过验证，直接返回成功
if (s) {
  console.log("[API_VALIDATION] Skipping validation as requested");
  return {valid: true, message: "Validation skipped"};
}

// 如果没有API密钥，返回错误
if (!t || t.trim() === "") {
  return {valid: false, message: "API key is required"};
}

try {
  // 根据不同的提供商进行验证
  if (e.includes("openai.com") || e.includes("api.aihubmix.com")) {
    return await this.#validateOpenAICompatible(e, t);
  } else if (e.includes("anthropic.com")) {
    return await this.#validateAnthropic(e, t);
  } else if (e.includes("deepseek.com")) {
    return await this.#validateDeepSeek(e, t);
  } else if (e.includes("moonshot.cn")) {
    return await this.#validateMoonshot(e, t);
  } else if (e.includes("bigmodel.cn")) {
    return await this.#validateZhipu(e, t);
  } else {
    // 对于未知提供商，尝试OpenAI兼容的验证
    return await this.#validateOpenAICompatible(e, t);
  }
} catch(error) {
  console.error("[API_VALIDATION] Validation error:", error);
  return {
    valid: false,
    message: `Validation failed: ${error.message || 'Network error'}`
  };
}
};

// OpenAI兼容API验证（适用于OpenAI、aihubmix等）
#validateOpenAICompatible=async(baseUrl,apiKey)=>{
console.log("[API_VALIDATION] Validating OpenAI-compatible API");
try {
  // 智能处理URL，避免重复添加/v1
  let modelsUrl;
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '');

  if (cleanBaseUrl.includes('/v1') || cleanBaseUrl.includes('/api/')) {
    // 如果已经包含版本路径，直接添加/models
    modelsUrl = `${cleanBaseUrl}/models`;
  } else {
    // 否则添加/v1/models
    modelsUrl = `${cleanBaseUrl}/v1/models`;
  }

  console.log("[API_VALIDATION] Models URL:", modelsUrl);

  // 使用内置的HTTP请求方法
  const response = await this.#makeHttpRequest(modelsUrl, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
      'User-Agent': 'AiPy-Pro/1.0'
    },
    timeout: 10000
  });

  if (response.success && response.data) {
    const data = response.data;
    if (data.data && Array.isArray(data.data)) {
      console.log("[API_VALIDATION] OpenAI-compatible validation successful");
      return {valid: true, message: "API key is valid", models: data.data};
    }
  }

  console.log("[API_VALIDATION] OpenAI-compatible validation failed:", response.status, response.error);
  return {
    valid: false,
    message: `Invalid API key (${response.status}): ${response.error || 'Unknown error'}`
  };
} catch(error) {
  console.error("[API_VALIDATION] OpenAI-compatible validation error:", error);
  return {
    valid: false,
    message: `Connection failed: ${error.message}`
  };
}
};

// HTTP请求辅助方法
#makeHttpRequest=async(url,options)=>{
try {
  // 在Electron环境中使用内置的net模块或者简化的请求
  const https = require('https');
  const http = require('http');
  const urlParse = require('url').parse;

  return new Promise((resolve, reject) => {
    const parsedUrl = urlParse(url);
    const isHttps = parsedUrl.protocol === 'https:';
    const client = isHttps ? https : http;

    const requestOptions = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.path,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 10000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: jsonData,
            error: res.statusCode >= 400 ? data : null
          });
        } catch(e) {
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data,
            error: res.statusCode >= 400 ? data : null
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
} catch(error) {
  throw error;
}
};

// Anthropic API验证
#validateAnthropic=async(baseUrl,apiKey)=>{
console.log("[API_VALIDATION] Validating Anthropic API");
try {
  // 智能处理URL，避免重复添加/v1
  let messagesUrl;
  const cleanBaseUrl = baseUrl.replace(/\/+$/, '');

  if (cleanBaseUrl.includes('/v1')) {
    // 如果已经包含/v1，直接添加/messages
    messagesUrl = `${cleanBaseUrl}/messages`;
  } else {
    // 否则添加/v1/messages
    messagesUrl = `${cleanBaseUrl}/v1/messages`;
  }

  console.log("[API_VALIDATION] Messages URL:", messagesUrl);

  // Anthropic没有models端点，我们发送一个简单的消息请求来验证
  const response = await this.#makeHttpRequest(messagesUrl, {
    method: 'POST',
    headers: {
      'x-api-key': apiKey,
      'Content-Type': 'application/json',
      'anthropic-version': '2023-06-01',
      'User-Agent': 'AiPy-Pro/1.0'
    },
    body: JSON.stringify({
      model: 'claude-3-haiku-20240307',
      max_tokens: 1,
      messages: [{role: 'user', content: 'test'}]
    }),
    timeout: 10000
  });

  if (response.success || response.status === 400) {
    // 400也可能表示API密钥有效但请求格式有问题
    console.log("[API_VALIDATION] Anthropic validation successful");
    return {valid: true, message: "API key is valid"};
  }

  console.log("[API_VALIDATION] Anthropic validation failed:", response.status, response.error);
  return {
    valid: false,
    message: `Invalid API key (${response.status}): ${response.error || 'Unknown error'}`
  };
} catch(error) {
  console.error("[API_VALIDATION] Anthropic validation error:", error);
  return {
    valid: false,
    message: `Connection failed: ${error.message}`
  };
}
};

// DeepSeek API验证
#validateDeepSeek=async(baseUrl,apiKey)=>{
console.log("[API_VALIDATION] Validating DeepSeek API");
// DeepSeek使用OpenAI兼容的API
return await this.#validateOpenAICompatible(baseUrl, apiKey);
};

// Moonshot API验证
#validateMoonshot=async(baseUrl,apiKey)=>{
console.log("[API_VALIDATION] Validating Moonshot API");
// Moonshot使用OpenAI兼容的API
return await this.#validateOpenAICompatible(baseUrl, apiKey);
};

// 智谱AI验证
#validateZhipu=async(baseUrl,apiKey)=>{
console.log("[API_VALIDATION] Validating Zhipu API");
// 智谱AI使用OpenAI兼容的API
return await this.#validateOpenAICompatible(baseUrl, apiKey);
};ttRequestBind=async()=>C(`${this.#s.endpoints.tt.www}/api/request_bind`,{method:"POST"}).then(e=>e.body);ttCheckBind=async e=>C(`${this.#s.endpoints.tt.www}/api/check_status?${new URLSearchParams({request_id:e})}`).then(t=>t.body);#r=null;#o=async(e,t={},s=!0)=>{
// 离线模式：跳过Trustoken相关的网络请求
console.log("[OFFLINE] Skipping Trustoken API request:", e);
throw new Error("Offline mode: Trustoken API requests are disabled");
// this.#r||(this.#r=await this.#e.ttToken()),s&&this.#r.expires_at-604800<Date.now()&&this.#y(this.#r);let i=this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_API)?.value;if(!i)throw this.showLogin(),new Error("Trustoken API not configured, please login first.");e.startsWith("/api/")&&(i=i.replace(/^https:\/\/s?api/,"https://www"));const n=await C(i+e,{...t,headers:s?{Authorization:`Bearer ${this.#r.access_token}`,...t.headers||{},"New-Api-User":this.#r["New-Api-User"]}:t.headers||{}});let r=n.body;if(r?.success===!0&&r?.data)r=r.data;else if(r?.success===!1&&r?.message){const o=new Error(r.message);throw o.status=n.status,o.statusText=n.statusText,o.headers=n.headers,o.body=r,console.error({url:e,status:n.status,payload:t.body,repsonse:r,error:o},"ttFetch"),o}return r
};#y=async()=>{};profile=N(async()=>{
// 离线模式：返回虚拟用户数据
console.log("[OFFLINE] Returning mock profile data");
const mockProfile = {
  id: "offline-user",
  username: "offline-user",
  email: "<EMAIL>",
  name: "Offline User"
};
P["X-AIPY-UID"] = mockProfile.id;
P["X-AIPY-USERNAME"] = mockProfile.username;
return mockProfile;
// let e;try{return e=await this.#o("/api/user/self"),this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_PROFILE,JSON.stringify(e)),e}catch(t){console.error(t,"FETCH-PROFILE");try{e=JSON.parse(this.#e.metaFindByField.get(f.TRUSTOKEN_PROFILE)?.value)}catch{this.logout()}}return P["X-AIPY-UID"]=e?.id||"",P["X-AIPY-USERNAME"]=e?.username||"",e
});ttApiKey=x(f.TRUSTOKEN_KEY,async()=>{const e=await this.#o("/api/token/?p=0&size=100"),{key:t}=e.find(s=>s.status===1)||{key:""};if(!t)throw new Error("No valid API key found.");return t});ttProviderSync=N(async()=>{const e=await this.ttApiKey(),t=this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_API)?.value||"",s=this.#e.prepare(`
			SELECT name, model, max_tokens
			FROM provider
			WHERE type = 'trust' AND is_custom = 0
		`).get();return this.#a={type:"trust",name:s?.name||"trustoken",base_url:`${t}/v1`,api_key:e,model:s?.model||"auto",max_tokens:s?.max_tokens||8192},this.ttModels()});ttModels=x(f.TRUSTOKEN_MODELS,async()=>{const e=JSON.parse(this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_KEY)?.value||'""');return this.#o("/v1/models",{headers:{Authorization:`Bearer sk-${e}`}},!1).then(t=>(t.find(s=>s.id===this.#a.model)||(this.#a.model="auto",this.#e.exec("UPDATE provider SET model = 'auto' WHERE type = 'trust' AND is_custom = 0")),t))});#a={};ttSync=N(async()=>{
// 离线模式：跳过Trustoken同步，初始化本地提供商信息
console.log("[OFFLINE] Skipping Trustoken sync, initializing local provider info");
try {
  // 初始化提供商信息字典
  const providerInfo = await this.providerInfoList();
  this.#d = providerInfo.reduce((acc, provider) => {
    acc[provider.id] = provider;
    return acc;
  }, {});
  console.log("[OFFLINE] Provider info initialized:", Object.keys(this.#d).length, "providers");

  // 初始化默认的Trustoken提供商配置（离线模式）
  this.#a = {
    type: "trust",
    name: "offline-provider",
    base_url: "http://localhost:8080/v1", // 默认本地地址
    api_key: "offline-key",
    model: "auto",
    max_tokens: 8192
  };

  // 加载自定义提供商
  await this.providerCustomList();

  // 检查更新（可选）
  this.#l.checkForUpdates();
} catch(e) {
  console.log("[OFFLINE] Initialization failed, continuing in offline mode:", e);
}
return Promise.resolve();
// (this.#l.checkForUpdates(),Promise.all([this.ttProviderSync(),this.providerCustomList(),this.providerInfoList().then(e=>{this.#d=e.reduce((t,s)=>(t[s.id]=s,t),{})}),this.roleList()]).catch(()=>{this.logout()}))
});#p={};#u="";#T=()=>`${this.#s.general.lang==="zh"?b.trustoken.cn.www:b.trustoken.ai.www}/api/oauth/authorize?${new URLSearchParams({code_challenge:this.#p.code_challenge,code_challenge_method:"S256",response_type:"code",redirect_uri:F.redirect_uri,scope:F.scope,client_id:F.client_id})}`;showLogin=N(async()=>{
// 离线模式：不再跳转到登录页面，直接返回
console.log("[LOGIN] Offline mode: Login disabled");
return;
// this.#p=await W.default();const e=this.#T();console.log("[LOGIN]",e,this.#u=this.#i.webContents.getURL()),this.#i.webContents.loadURL(e)
});logout=N(async()=>{this.#r=null,this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_TOKEN,"{}"),this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_PROFILE,"{}"),this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_KEY,'""'),this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_CUSTOM_PROVIDER,"[]"),this.#p=await W.default();const e=this.#T(),t=`${this.#s.endpoints.tt.www}/api/oauth/logout?${new URLSearchParams({next:e})}`;console.log("[LOGOUT]",URL,this.#u=this.#i.webContents.getURL()),this.#i.webContents.loadURL(t)});oauthCallback=N(async(e,t)=>{const s=t==="cn"?b.trustoken.cn.www:b.trustoken.ai.www;try{const n=(await C(`${s}/api/oauth/token`,{method:"POST",headers:{"content-type":"application/x-www-form-urlencoded"},body:new URLSearchParams({code:e,redirect_uri:F.redirect_uri,client_id:F.client_id,code_verifier:this.#p.code_verifier,grant_type:"authorization_code"})})).body;n.expires_at=Date.now()+n.expires_in*1e3,this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_TOKEN,JSON.stringify(n)),this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_API,t==="cn"?b.trustoken.cn.api:b.trustoken.ai.api),this.#s.loadEndpoints(),this.#r=null,this.#i.webContents.loadURL(this.#u)}catch(i){throw console.error("[OAUTH-CALLBACK]",i),this.logout(),new Error("Failed to get access token from Trustoken.")}});providerMigrateCheck=()=>this.#e.stmt.metaFindByField.get(f.TRUSTOKEN_PROVIDER_MIGRATED)?.value==="0";providerMigrateMark=(e="1")=>{this.#e.stmt.metaUpsertByField.run(f.TRUSTOKEN_PROVIDER_MIGRATED,e)};roleList=x(f.AIPY_ROLES,()=>this.#o("/api/ai-role").then(e=>e.map(({id:t,role_name:s,role:i})=>({id:t,name:s,role:JSON.parse(i)}))));updateCheck=async(e=!1)=>(await this.#s.waitForLoaded(),this.#l.checkForUpdates(e));updateDownload=async()=>this.#l.download();openWorkDirectory=async e=>{let t;if(e){const s=this.getTaskById(e);u.existsSync(s.workdir)&&(t=s.workdir)}t||(t=this.#s.general.workdir),console.log("[openWorkDirectory]",t),d.shell.openPath(t)};readdir=async e=>{if(!e||!u.existsSync(e))return;const t=u.readdirSync(e,{recursive:!1,withFileTypes:!0,encoding:"utf-8"});let s=[];for(const i of t){if(i.isDirectory())i.type="directory";else if(i.isFile())i.type="file";else continue;const n=p.join(i.parentPath,i.name);let r=i.parentPath.slice(e.length+1);const o=u.statSync(n,{throwIfNoEntry:!1});s.push({parentPath:r,name:i.name,type:i.type,ctime:o.birthtimeMs})}return s.sort((i,n)=>i.type!==n.type?i.type==="directory"?-1:1:n.ctime-i.ctime),{data:s,sep:p.sep}};#g=x(f.EXTENSIONS,()=>this.#o("/api/application"),{policy:"cache-first"});extensionStoreList=async()=>{const e=(globalThis.ext?.extensions||[]).reduce((i,n)=>(i[n.name]=n.path&&u.existsSync(n.path)?n:null,i),{}),t=await this.#g();let s=[];for(const{name:i,version:n,type:r,display_name:o,dxt_url:c,homepage:l,description:T,keywords:y}of t){const E=e[i],L=E?K(E.version)<K(n):!1;L&&globalThis.ext&&globalThis.ext.install({type:r,dxtUrl:c,homepage:l,description:T,keywords:y}),s.push({name:i,version:n,type:r,displayName:o,dxtUrl:c,homepage:l,description:T,keywords:y,isInstalled:!!E,installedVersion:E?E.version:"",updateAvailable:L})}return s};extensionToggle=async(e,t)=>{if(!globalThis.ext||!e||!t)return;let s=globalThis.ext.extensions.find(i=>i.name===t);return s.isRunning||(await globalThis.ext.start(t),s=globalThis.ext.extensions.find(i=>i.name===t)),this.#t?.[e]?.isRunning||await this.resumeTask(e,{}),await this.#t[e].waitHttpStart(),await this.#t[e].loadMcpServer([{name:t,tools:s.tools||[],url:s.serverUrl,transport:{type:"streamable_http"},headers:{},enabled:!0}]),this.mcpToggle(e,{[t]:!0})};waitStdinOpen=e=>this.#t?.[e].waitStdinOpen();info=()=>({version:d.app.getVersion(),aipyappVersion:this.aipyappVersion,configDir:this.#n,resourcesPath:_,isPackaged:d.app.isPackaged});getSuggestInstructionsInfo=async()=>{try{const e=await this.#e.stmt.metaFindByField.get(f.SUGGEST_INSTRUCTIONS);return this.suggestInstructions(),JSON.parse(e.value)[this.#s.general.lang]}catch(e){return console.error(e,"获取推荐提示词时出错!"),[]}};suggestInstructions=()=>{
// 离线模式：使用本地默认推荐指令
console.log("[OFFLINE] Using local suggest instructions");
const defaultInstructions = {
  zh: [
    {instruction: "分析当前电脑CPU、内存占用TOP10软件有哪些？"},
    {instruction: "写本1000字短篇小说《我的AI女友》，保存为HTML在当前目录下，保存好后，使用电脑默认浏览器打开它。"},
    {instruction: "分析我电脑安装了哪些软件，借此推判我的职业和爱好。"}
  ],
  en: [
    {instruction: "Analyze the top 10 software consuming CPU and memory on my computer."},
    {instruction: 'Write a 1000-word short story titled "My AI Girlfriend", save it as HTML in the current directory, and open it in the default web browser.'},
    {instruction: "Analyze the software installed on my computer to infer my profession and hobbies."}
  ]
};
try {
  this.#e.stmt.metaUpsertByField.run("suggest-instuctions", JSON.stringify(defaultInstructions));
} catch(e) {
  console.error("[suggestInstructions] Failed to save default instructions:", e);
}
return Promise.resolve();
// C(`${this.#s.endpoints.aipy.www}/api/prompt/suggest.json`).then(e=>e.body).then(async e=>{if(!e||Object.keys(e).length===0)throw new Error("暂时没有推荐词数据");for(const t in e)e[t]=Ie(e[t]);this.#e.stmt.metaUpsertByField.run("suggest-instuctions",JSON.stringify(e))}).catch(e=>{console.error("[suggestInstructions]",e)})
};loadRole=async(e,t)=>{const s={name:t};if(t!=="aipy"&&t!=="dialogue"){const n=(await this.roleList()).find(r=>r.name===t);n?s.value=n.role:(console.error(w.l10n`Role ${t} not found`),s.name="aipy")}this.#t?.[e]?.isRunning&&await this.#t[e].loadRole(s),this.#e.prepare("UPDATE task SET role = ? WHERE id = ?").run(t,e)};report=$;ttToken=async()=>{
// 离线模式：返回虚拟token数据
console.log("[OFFLINE] Returning mock token data");
return {
  access_token: "offline-mode-token",
  expires_at: Date.now() + 86400000, // 24小时后过期
  "New-Api-User": "offline-user"
};
// (this.#r||(this.#r=await this.#e.ttToken()),this.#r)
}}function x(a,e,t={policy:"function-first"}){return N(async(...s)=>{try{const i=globalThis.db.stmt.metaFindByField.get(a)?.value;let n=null;if(i)try{n=JSON.parse(i)}catch{}if(t.policy==="cache-first"&&n!==null)return e(...s).then(o=>{globalThis.db.stmt.metaUpsertByField.run(a,JSON.stringify(o))}),n;const r=await e(...s);return globalThis.db.stmt.metaUpsertByField.run(a,JSON.stringify(r)),r}catch(i){console.error(i,a);try{return}catch{throw i}}})}class ve{#e=[];#n="";constructor(){this.#n=p.join(d.app.getPath("userData"),"extensions"),u.existsSync(this.#n)||u.mkdirSync(this.#n,{recursive:!0}),this.#t()}get extensions(){return this.#e.map(e=>({...e,process:e?.process?.pid}))}#t(){const e=u.globSync("*/*/manifest.json",{cwd:this.#n,absolute:!0});for(const t of e)try{const s=p.join(this.#n,t),{display_name:i,...n}=JSON.parse(u.readFileSync(s,"utf-8")),r=n.name;n.path=p.dirname(s);const o=this.#e.find(c=>c.name===r);o?Object.assign(o,{...n,displayName:i,icon:n.icon?new URL(`file://${p.join(n.path,n.icon)}`).toString():"",isRunning:!1}):this.#e.push({...n,displayName:i,icon:n.icon?new URL(`file://${p.join(n.path,n.icon)}`).toString():"",isRunning:!1})}catch(s){console.error({error:s,file:t},"Failed to load extension")}}install=async({description:e,dxtUrl:t,keywords:s,homepage:i,type:n="conversation-tool"})=>{const r=new URL(t),o=r.pathname.split("/").slice(-1)[0],c=p.join(this.#n,".tmp",o);await ye(r,c);let l;try{const T=new de(c);l=JSON.parse(T.readAsText("manifest.json")),Object.assign(l,{description:e,homepage:i,keywords:[n].concat(s||[])});const y=p.join(this.#n,".tmp",l.name);u.mkdirSync(y,{recursive:!0}),T.extractAllTo(y,!0);const E=p.join(this.#n,l.name);u.existsSync(E)&&u.rmSync(E,{recursive:!0,force:!0});const L=p.dirname(E);u.existsSync(L)||u.mkdirSync(L,{recursive:!0}),u.renameSync(y,E),u.writeFileSync(p.join(E,"manifest.json"),JSON.stringify(l,null,2)),this.#e=this.#e.filter(te=>te.name!==l.name).concat(Object.assign({...l,display_name:void 0,displayName:l.display_name,path:p.join(E),icon:l.icon?new URL(`file://${p.join(E,l.icon)}`).toString():""}))}catch(T){throw console.error({error:T.stack,dxtFilePath:c},"Failed to install Extension"),new Error(`Failed to install Extension: ${c}`,{cause:T})}return l};uninstall=async e=>{const t=this.#e.find(s=>s.name===e);t||(console.error({extName:e},"Uninstall Extension not found"),reutrn),t.process&&!t.process.killed&&t.process.kill(),u.existsSync(t.path)&&(u.rmSync(t.path,{recursive:!0,force:!0}),console.info({extName:e},"Extension uninstalled successfully")),this.#e=this.#e.filter(s=>s.name!==e)};async start(e){const t=this.#e.find(o=>o.name===e);if(!t)throw console.error({extName:e},"Extension not found"),new Error(`Extension not found: ${e}`);if(t.path||await this.install(e),t.process&&!t.process.killed){console.warn({extName:e},"Extension is already running");return}const s=p.join(t.path,t.server.entry_point);if(!u.existsSync(s))throw new Error(`Extension entry file not found: ${s}`);const i=globalThis.db.stmt.metaFindByField.get(f.TRUSTOKEN_API)?.value,n=JSON.parse(globalThis.db.stmt.metaFindByField.get(f.TRUSTOKEN_KEY)?.value||'""'),r=G.fork(s,[],{stdio:["pipe","pipe","pipe","ipc"],env:{TRUSTOKEN_API:i,TRUSTOKEN_API_KEY:n,AIPY_HEADERS:JSON.stringify(P)}});return t.process=r,t.isRunning=!0,new Promise((o,c)=>{r.on("message",async l=>{if(console.info({message:l,name:t.name},"Message from extension"),l.type==="http_start")try{const T=new pe.Client(t),y=`http://127.0.0.1:${l.port}/mcp`;await T.connect(new ue.StreamableHTTPClientTransport(new URL(`http://127.0.0.1:${l.port}/mcp`))),t.tools=await T.listTools().then(E=>E.tools||[]),t.serverUrl=y,console.log({extName:e},"Connected to extension server"),o(t)}catch(T){console.error({error:T.stack,extName:e},"Failed to connect to extension")}}),r.on("error",l=>{console.error({error:l,extName:e},"Error from extension"),c()}),r.on("exit",l=>{console.error({code:l,extName:e},"Extension exited with code"),t.isRunning=!1,t.process=null,t.serverUrl="",c()}),r.stderr.on("data",l=>{console.info({extName:e,value:l.toString()},"Extension stderr")}),r.stdout.on("data",l=>{console.info({extName:e,value:l.toString()},"Extension stdout")}),console.info({extName:e},"Extension started successfully")})}stop(e){const t=this.#e.find(s=>s.name===e);t.process&&(t.process.kill(),t.isRunning=!1,t.process=null,t.serverUrl="",console.info({extName:e},"Extension with stopped."))}dispose(){for(const e of this.#e)e.process&&(e.process.kill(),e.isRunning=!1,e.process=null,e.serverUrl="",console.info({name:e.name},"Extension with stopped."))}}w.default.defaultLocale=d.app.getLocale().includes("zh")?"zh":"en";w.default.templates.zh={"AiPy Update":"爱派更新","AiPy updates {} downloaded, Quit and update now?":"更新 {} 已下载，立即安装？",OK:"好的",Cancel:"取消",Copy:"复制",Paste:"粘贴",Cut:"剪切","Select All":"全选","Failed to sync custom model":"同步自定义模型配置失败","Regsiter Custom URL Protocol":"注册自定义 URL 协议",'Would you like to set {} as the default application to open "{}://" links?':'是否将 {} 设置为打开 "{}://" 链接的默认应用？',"For Login or use shared tasks, you need to set this app as the default application to handle the custom URL protocol.":"登录或使用共享任务时，需要将此应用设置为处理自定义 URL 协议的默认应用。","Copy Path":"复制路径","Work directory does not exist: {}":"工作目录不存在: {}"};const z="aipy-pro";le.once(()=>{d.app.isDefaultProtocolClient(z)||(process.isPackaged?d.app.setAsDefaultProtocolClient(z):d.app.setAsDefaultProtocolClient(z,process.execPath,[p.join(process.cwd(),"out/main/index.js")]))});const Ae=`
	<head>
	<style>
		body {
			width: 100vw
			height: 100vh
			margin: 0
			display: flex
			justify-content: center
			align-items: center
		}
		.loading {
			pointer-events: none
			display: inline-block
			aspect-ratio: 1 / 1
			vertical-align: middle
			background-color: #1976d2
			width: 32px
			mask-size: 100%
			mask-repeat: no-repeat
			mask-position: center
			mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,15042,15042,150' keyTimes='00.4751' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0-16-59' keyTimes='00.4751' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E")
		}
	</style>
	</head>
	<body><div class="loading"></div></body>
`;function Ne(){d.app.on("open-url",(a,e)=>{a.preventDefault(),console.log(`[open-url]: ${e}`);let t=new URL(e);switch(t.hostname){case"oauth":setTimeout(()=>{globalThis.aipy.oauthCallback(t.searchParams.get("code"),t.searchParams.get("site")||"ai")})}}),d.app.on("second-instance",async(a,e,t)=>{console.log("second-instance",{event:a,commandLine:e,workingDirectory:t});for(let s of e)if(s.startsWith(`${z}:`)){const i=new URL(s),n=i.searchParams.get("code"),r=i.searchParams.get("site")||"ai";if(!n)return;await globalThis.aipy.oauthCallback(n,r)}}),d.protocol.handle(z,a=>{let e=Ae;const t=new URL(a.url);switch(t.hostname){case"oauth":return setTimeout(()=>{globalThis.aipy.oauthCallback(t.searchParams.get("code"),t.searchParams.get("site")||"ai")}),new Response(e.replace('content: "Loading..."','content: "Synchronizing..."'),{status:200,statusText:"OK",headers:{"Content-Type":"text/html"}});default:return new Response(e,{status:200,statusText:"OK",headers:{"Content-Type":"text/html"}})}})}class Le{cwd=D;process=null;error=null;window=null;constructor(e){this.window=e}get isRunning(){return this.process?.stdin&&!this.process?.stdin?.closed&&!this.process?.stdin?.destroyed}spawn=()=>{try{let e=Z;if(!e)switch(k.platform()){case"win32":e="C:\\Windows\\System32\\cmd.exe";break;case"darwin":e="/bin/zsh";break;default:e="/bin/sh"}const t={...process.env,PIP_TARGET:R,PYTHONPATH:Q,PYTHONIOENCODING:"UTF-8"};if(k.platform()==="win32"){const s="C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe";u.existsSync(s)&&(e=s),t.Path=[p.join(_,"python/Scripts"),p.join(_,"python"),process.env.Path].join(p.delimiter)}else t.PATH=[p.join(_,"python/bin"),process.env.PATH].join(p.delimiter),t.LANG||(t.LANG="en_US.UTF-8");this.process=he.spawn(e,[],{env:t,name:"xterm-color",cols:80,rows:30,cwd:this.cwd}),this.process.onExit((s,i)=>{this.process=null,console.log("[terminal:exit]",{exitCode:s,signal:i})}),this.process.onData(s=>{!this.window||this.window.isDestroyed()||this.window.webContents.send("terminal:stdout",s)})}catch(e){this.error=e,console.error("[terminal:spawn] Error:",e)}};exit=()=>{try{this.process.kill()}catch(e){console.error("[terminal-kill]",e)}this.process=null};write=e=>{this.process||this.spawn(),this.process.write(e)};resize=({cols:e,rows:t})=>{this.process&&this.process.resize(e,t)};chdir=e=>{if(u.existsSync(e)===!1)throw new Error(`Path does not exist: ${e}`);if(!u.statSync(e,{throwIfNoEntry:!1}).isDirectory())throw new Error(`Path is not a directory: ${e}`);e!==this.cwd&&(this.process?(this.process.write(`cd ${JSON.stringify(e)}\r`),this.process.write("\f"),this.cwd=e):this.cwd=e)}}function Ue(a){const e=d.Menu.buildFromTemplate([{label:w.l10n`Copy`.toString(),role:"copy"}]),t=d.Menu.buildFromTemplate([{label:w.l10n`Cut`.toString(),role:"cut"},{label:w.l10n`Copy`.toString(),role:"copy"},{label:w.l10n`Paste`.toString(),role:"paste"},{type:"separator"},{label:w.l10n`Select All`.toString(),role:"selectAll"}]);a.webContents.on("context-menu",(s,i)=>{if(console.log({event:s,params:i},"context-menu"),i.isEditable)t.popup({window:a});else if(i.selectionText)e.popup({window:a});else if(i.mediaType==="image"||i.mediaType==="video"){if(i.srcURL.startsWith("data:"))return;const{protocol:n,pathname:r}=new URL(i.srcURL);d.Menu.buildFromTemplate([{label:w.l10n`Copy Path`.toString(),click:()=>{n==="file:"?d.clipboard.writeText(process.platform==="win32"?r.slice(1):r):d.clipboard.writeText(i.srcURL)}}]).popup({window:a})}})}d.app.requestSingleInstanceLock()||(console.log("Another instance is running, exiting..."),d.app.quit());function Y(){const a=d.screen.getPrimaryDisplay(),{width:e,height:t}=a.workAreaSize,s=new d.BrowserWindow({icon:ge,width:Math.min(Math.ceil(e*.9),1400),height:Math.min(Math.ceil(t*.8)),show:!1,autoHideMenuBar:!0,webPreferences:{preload:p.join(__dirname,"../preload/index.js"),sandbox:!1,webSecurity:!1,nodeIntegration:!0,contextIsolation:!1,webviewTag:!0}});return s.on("ready-to-show",()=>{s.show(),d.app.isPackaged||s.webContents.openDevTools()}),s.webContents.setWindowOpenHandler(i=>(d.shell.openExternal(i.url),{action:"deny"})),!d.app.isPackaged&&process.env.ELECTRON_RENDERER_URL?(console.log("load url",process.env.ELECTRON_RENDERER_URL),s.loadURL(process.env.ELECTRON_RENDERER_URL)):(console.log("load file",process.cwd(),p.join(__dirname,"../renderer/index.html")),s.loadFile(p.join(__dirname,"../renderer/index.html"))),s}let S=null;d.app.whenReady().then(async()=>{Ne(),q.electronApp.setAppUserModelId("app.aipy"),d.app.on("browser-window-created",(t,s)=>{q.optimizer.watchWindowShortcuts(s)});let a,e;try{const t=p.join(d.app.getPath("userData"),"aipy");a=globalThis.db=new be(t),await a.init(),e=new ke(a),await e.load(),H("settings",e),w.default.defaultLocale=e.general.lang}catch(t){console.error(t,"Failed to initialize settings or database:"),process.exit(1)}S=globalThis.mainWindow=Y(),Pe(S),Ue(S),H("terminal",globalThis.terminal=new Le(S),{write:!1,resize:!1}),H("ext",globalThis.ext=new ve,{}),H("aipy",globalThis.aipy=new _e(a,S,e),{workdirList:null,getTaskById:null,getTaskList:null,providerList:null,roleList:null,ttSync:!1,getSuggestInstructionsInfo:null,mcpStatus:null}),globalThis.aipy.extensionStoreList(),S.on("ready-to-show",()=>{globalThis.terminal.spawn()}),S.webContents.on("did-fail-load",(t,s,i,n,r)=>{if(console.log({event:t,errorCode:s,errorDescription:i,validatedURL:n,isMainFrame:r},"did-fail-load"),r&&n.includes("https://www.trustoken.")){const o=new URLSearchParams({errorCode:s,errorDescription:i,validatedURL:n});!d.app.isPackaged&&process.env.ELECTRON_RENDERER_URL?S.loadURL(`${process.env.ELECTRON_RENDERER_URL}/load-fail.html?${o}`):S.loadFile(p.join(__dirname,"../renderer/load-fail.html"),{search:`?${o}`})}}),d.app.on("activate",()=>{console.log("activate"),d.BrowserWindow.getAllWindows().length===0&&Y()}),d.app.on("window-all-closed",()=>{globalThis.terminal.exit(),globalThis.aipy.stop()})});d.app.on("window-all-closed",()=>{console.log("window-all-closed"),globalThis.ext?.dispose?.(),process?.close?.(),d.app.quit()});d.app.on("second-instance",async()=>{S&&(S.isMinimized()&&S.restore(),S.focus())});function Pe(a){d.ipcMain.handle("dialog:showOpenDialog",async(e,t)=>{const{canceled:s,filePaths:i}=await d.dialog.showOpenDialog(a,t);if(!s)return i}),d.ipcMain.handle("shell:trashItem",async(e,t)=>d.shell.trashItem(Array.isArray(t)?p.join(...t):t)),d.ipcMain.handle("shell:openPath",async(e,t)=>d.shell.openPath(Array.isArray(t)?p.join(...t):t)),d.ipcMain.handle("app:relaunch",()=>{d.app.relaunch({args:process.argv.slice(1).concat(["--relaunch"])}),d.app.exit(0)}),d.ipcMain.handle("openDevTools",()=>{a.webContents.openDevTools()})}
