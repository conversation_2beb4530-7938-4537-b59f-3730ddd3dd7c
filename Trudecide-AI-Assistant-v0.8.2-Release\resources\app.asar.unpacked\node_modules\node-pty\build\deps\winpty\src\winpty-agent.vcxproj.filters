<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\agent">
      <UniqueIdentifier>{8474C169-9C01-C09B-BA71-B21D816D6E84}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src\shared">
      <UniqueIdentifier>{20D50607-EB73-BC01-11AE-A2B6030BB0A1}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:">
      <UniqueIdentifier>{7B735499-E5DD-1C2B-6C26-70023832A1CF}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users">
      <UniqueIdentifier>{E9F714C1-DA89-54E2-60CF-39FEB20BF756}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\Users\Docker\aipy">
      <UniqueIdentifier>{33D01F04-3700-338B-4B3E-97E8ECFAC98E}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\aipy\desktop">
      <UniqueIdentifier>{FC4CCBFB-F86E-DBC0-093A-D8F87293D843}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\aipy\desktop\node_modules">
      <UniqueIdentifier>{56DF7A98-063D-FB9D-485C-089023B4C16A}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\aipy\desktop\node_modules\@electron">
      <UniqueIdentifier>{6FD122B1-87CE-5BE7-127F-692B6E499EA3}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\aipy\desktop\node_modules\@electron\node-gyp">
      <UniqueIdentifier>{77348C0E-2034-7791-74D5-63C077DF5A3B}</UniqueIdentifier>
    </Filter>
    <Filter Include="C:\Users\<USER>\aipy\desktop\node_modules\@electron\node-gyp\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..">
      <UniqueIdentifier>{739DB09A-CC57-A953-A6CF-F64FA08E4FA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps">
      <UniqueIdentifier>{D8C1C9B5-38B1-FC5D-54EB-0CB99232FA56}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty">
      <UniqueIdentifier>{65CA4A4C-2F90-FF0A-086F-3A4E81B2F5F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="..\..\..\..\deps\winpty\src">
      <UniqueIdentifier>{8CDEE807-BC53-E450-C8B8-4DEBB66742D4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Agent.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Agent.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\AgentCreateDesktop.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\AgentCreateDesktop.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleFont.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleFont.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleInput.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleInput.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleInputReencoding.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleInputReencoding.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\ConsoleLine.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\ConsoleLine.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Coord.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DebugShowInput.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\DebugShowInput.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DefaultInputMap.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\DefaultInputMap.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\DsrSender.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\EventLoop.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\EventLoop.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\InputMap.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\InputMap.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\LargeConsoleRead.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\LargeConsoleRead.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\NamedPipe.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\NamedPipe.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Scraper.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Scraper.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\SimplePool.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\SmallRect.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Terminal.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Terminal.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\UnicodeEncoding.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Win32Console.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Win32Console.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\Win32ConsoleBuffer.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\agent\Win32ConsoleBuffer.h">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\agent\main.cc">
      <Filter>..\..\..\..\deps\winpty\src\agent</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\AgentMsg.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\BackgroundDesktop.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\BackgroundDesktop.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\Buffer.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\Buffer.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\DebugClient.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\DebugClient.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\GenRandom.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\GenRandom.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\OsModule.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\OwnedHandle.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\OwnedHandle.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\StringBuilder.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\StringUtil.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\StringUtil.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\UnixCtrlChars.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WindowsSecurity.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WindowsSecurity.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WindowsVersion.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WindowsVersion.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyAssert.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyAssert.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyException.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyException.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\WinptyVersion.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="..\..\..\..\deps\winpty\src\shared\WinptyVersion.cc">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClCompile>
    <ClInclude Include="..\..\..\..\deps\winpty\src\shared\winpty_snprintf.h">
      <Filter>..\..\..\..\deps\winpty\src\shared</Filter>
    </ClInclude>
    <ClCompile Include="C:\Users\<USER>\aipy\desktop\node_modules\@electron\node-gyp\src\win_delay_load_hook.cc">
      <Filter>C:\Users\<USER>\aipy\desktop\node_modules\@electron\node-gyp\src</Filter>
    </ClCompile>
    <None Include="..\..\..\..\deps\winpty\src\winpty.gyp">
      <Filter>..\..\..\..\deps\winpty\src</Filter>
    </None>
  </ItemGroup>
</Project>
