@echo off

rem -- Echo the git commit hash.  If git isn't available for some reason,
rem -- output nothing instead.

mkdir ..\gen 2>nul

set /p VERSION=<..\..\VERSION.txt
set COMMIT=%1

echo // AUTO-GENERATED BY %0 %*>..\gen\GenVersion.h
echo const char GenVersion_Version[] = "%VERSION%";>>..\gen\GenVersion.h
echo const char GenVersion_Commit[] = "%COMMIT%";>>..\gen\GenVersion.h

rem -- The winpty.gyp file expects the script to output the include directory,
rem -- relative to src.
echo gen

rem -- Set ERRORLEVEL to 0 using this cryptic syntax.
(call )
