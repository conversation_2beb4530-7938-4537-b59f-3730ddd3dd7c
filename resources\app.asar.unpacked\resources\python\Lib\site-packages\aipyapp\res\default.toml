workdir = 'work'
share_result = true
auto_install = true

[diagnose]
# 离线模式：禁用诊断服务的云端上报
enable = false
api_url = ""
api_key = ""

[llm.trustoken]
type = "trust"
api_key = ""
enable = false
# 离线模式：Trustoken服务已禁用

[llm.aihubmix]
type = "aihubmix"
api_base = "https://api.aihubmix.com/v1"
api_key = "sk-dtFuF8Uo9uH3h3A621B46bA0C99544E18d2aC9E23107Ce5a"
model = "gpt-3.5-turbo"
max_tokens = 4096
enable = true

[llm.ollama]
type = "ollama"
api_base = "http://localhost:11434"
api_key = ""
enable = true

[mcp]
enable = true

