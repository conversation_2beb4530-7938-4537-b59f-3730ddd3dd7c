<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="60" viewBox="0 0 200 60" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="trudecideGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="30" cy="30" r="25" fill="url(#trudecideGradient)" opacity="0.1"/>
  
  <!-- 主图标 - 抽象的决策树/网络图标 -->
  <g transform="translate(15, 15)">
    <!-- 中心节点 -->
    <circle cx="15" cy="15" r="4" fill="url(#trudecideGradient)"/>
    
    <!-- 连接线 -->
    <path d="M15 15 L25 8 M15 15 L25 22 M15 15 L5 8 M15 15 L5 22" 
          stroke="url(#trudecideGradient)" stroke-width="2" opacity="0.8"/>
    
    <!-- 外围节点 -->
    <circle cx="25" cy="8" r="2.5" fill="#06b6d4"/>
    <circle cx="25" cy="22" r="2.5" fill="#06b6d4"/>
    <circle cx="5" cy="8" r="2.5" fill="#06b6d4"/>
    <circle cx="5" cy="22" r="2.5" fill="#06b6d4"/>
  </g>
  
  <!-- 品牌文字 -->
  <text x="65" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" 
        fill="url(#trudecideGradient)" filter="url(#glow)">Trudecide</text>
  <text x="65" y="42" font-family="Arial, sans-serif" font-size="10" 
        fill="#64748b">AI Assistant</text>
</svg>
