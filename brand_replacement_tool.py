#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI助手软件品牌重塑自动化工具
Brand Replacement Tool for AI Assistant Software
"""

import os
import json
import re
import shutil
import sys
from pathlib import Path
from datetime import datetime

class BrandReplacementTool:
    def __init__(self, config_file="brand_config.json"):
        """初始化品牌替换工具"""
        self.config_file = config_file
        self.backup_dir = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.config = self.load_config()
        
    def load_config(self):
        """加载品牌配置"""
        default_config = {
            "app_name": "your-ai-assistant",
            "display_name": "Your AI Assistant",
            "brand_name": "YourBrand",
            "brand_name_zh": "您的品牌",
            "author": "Your Team",
            "homepage": "https://your-domain.com",
            "team_name": "Your Team",
            "description": "Your AI Assistant - Intelligent Helper",
            "remove_trustoken": True,
            "custom_theme": {
                "primary_color": "#1976d2",
                "secondary_color": "#dc004e",
                "accent_color": "#00bcd4"
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
        else:
            # 创建默认配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            
        return default_config
    
    def create_backup(self):
        """创建备份"""
        print("📦 创建备份...")
        
        backup_files = [
            "extracted_app/package.json",
            "extracted_app/out/renderer/index.html",
            "extracted_app/out/renderer/assets/",
            "extracted_app/out/renderer/image/",
            "extracted_app/out/main/index.js"
        ]
        
        os.makedirs(self.backup_dir, exist_ok=True)
        
        for file_path in backup_files:
            if os.path.exists(file_path):
                if os.path.isdir(file_path):
                    dst_path = os.path.join(self.backup_dir, os.path.basename(file_path))
                    if os.path.exists(dst_path):
                        shutil.rmtree(dst_path)
                    shutil.copytree(file_path, dst_path)
                else:
                    shutil.copy2(file_path, self.backup_dir)
        
        print(f"✅ 备份已创建: {self.backup_dir}")
    
    def update_package_json(self):
        """更新package.json"""
        print("📝 更新package.json...")
        
        package_file = "extracted_app/package.json"
        if not os.path.exists(package_file):
            print(f"❌ 文件不存在: {package_file}")
            return False
        
        try:
            with open(package_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 更新基础信息
            data['name'] = self.config['app_name']
            data['displayName'] = self.config['display_name']
            data['description'] = self.config['description']
            data['author'] = self.config['author']
            data['homepage'] = self.config['homepage']
            
            with open(package_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print("✅ package.json 更新完成")
            return True
            
        except Exception as e:
            print(f"❌ 更新package.json失败: {e}")
            return False
    
    def update_html_title(self):
        """更新HTML标题"""
        print("📝 更新HTML标题...")
        
        html_file = "extracted_app/out/renderer/index.html"
        if not os.path.exists(html_file):
            print(f"❌ 文件不存在: {html_file}")
            return False
        
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换标题
            content = re.sub(r'<title>.*?</title>', 
                           f'<title>{self.config["display_name"]}</title>', content)
            
            # 替换描述
            content = re.sub(r'<meta name="description" content=".*?"', 
                           f'<meta name="description" content="{self.config["description"]}"', content)
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ HTML标题更新完成")
            return True
            
        except Exception as e:
            print(f"❌ 更新HTML标题失败: {e}")
            return False
    
    def replace_brand_text(self):
        """替换品牌文本"""
        print("📝 替换品牌文本...")
        
        # 查找JavaScript文件
        js_files = []
        assets_dir = "extracted_app/out/renderer/assets/"
        
        if os.path.exists(assets_dir):
            for file in os.listdir(assets_dir):
                if file.endswith('.js') and 'index-' in file:
                    js_files.append(os.path.join(assets_dir, file))
        
        if not js_files:
            print("❌ 未找到JavaScript文件")
            return False
        
        # 定义替换规则
        replacements = {
            '"AiPy"': f'"{self.config["brand_name"]}"',
            '"爱派"': f'"{self.config["brand_name_zh"]}"',
            '"AIPY Team"': f'"{self.config["team_name"]}"',
            '"AiPy Pro"': f'"{self.config["display_name"]}"',
            '"aipy.app"': f'"{self.config["homepage"].replace("https://", "").replace("http://", "")}"',
            'https://aipy.app': self.config["homepage"],
        }
        
        # 如果需要移除Trustoken
        if self.config.get("remove_trustoken", False):
            replacements.update({
                '"Trustoken"': '""',
                '"trustoken"': '""',
                'Trustoken': 'CustomProvider',
            })
        
        success_count = 0
        for js_file in js_files:
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                for old_text, new_text in replacements.items():
                    content = content.replace(old_text, new_text)
                
                if content != original_content:
                    with open(js_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ 已更新: {js_file}")
                    success_count += 1
                
            except Exception as e:
                print(f"❌ 更新文件失败 {js_file}: {e}")
        
        print(f"✅ 品牌文本替换完成，共更新 {success_count} 个文件")
        return success_count > 0
    
    def create_custom_theme(self):
        """创建自定义主题"""
        print("🎨 创建自定义主题...")
        
        theme_css = f"""
/* 自定义品牌主题 */
[data-theme="custom-brand"] {{
  --color-primary: {self.config['custom_theme']['primary_color']};
  --color-secondary: {self.config['custom_theme']['secondary_color']};
  --color-accent: {self.config['custom_theme']['accent_color']};
  --color-primary-content: #ffffff;
  
  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  
  /* 阴影效果 */
  --shadow-primary: 0 4px 20px rgba(25, 118, 210, 0.3);
}}

/* 深色主题 */
[data-theme="custom-brand-dark"] {{
  --color-primary: {self.config['custom_theme']['primary_color']};
  --color-secondary: {self.config['custom_theme']['secondary_color']};
  --color-accent: {self.config['custom_theme']['accent_color']};
  
  --color-base-100: #1a1a1a;
  --color-base-200: #2d2d2d;
  --color-base-300: #404040;
  --color-base-content: #ffffff;
}}

/* 现代化组件样式 */
.modern-card {{
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}}

.smooth-transition {{
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}}

.hover-lift:hover {{
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}}
"""
        
        try:
            theme_file = "extracted_app/out/renderer/assets/custom-theme.css"
            with open(theme_file, 'w', encoding='utf-8') as f:
                f.write(theme_css)
            
            print(f"✅ 自定义主题已创建: {theme_file}")
            return True
            
        except Exception as e:
            print(f"❌ 创建自定义主题失败: {e}")
            return False
    
    def generate_report(self):
        """生成替换报告"""
        report = f"""
# 品牌替换报告
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 配置信息
- 应用名称: {self.config['app_name']}
- 显示名称: {self.config['display_name']}
- 品牌名称: {self.config['brand_name']}
- 团队名称: {self.config['team_name']}
- 主页地址: {self.config['homepage']}

## 已完成的操作
- ✅ 创建备份: {self.backup_dir}
- ✅ 更新package.json
- ✅ 更新HTML标题
- ✅ 替换品牌文本
- ✅ 创建自定义主题

## 下一步操作
1. 替换Logo和图标文件
2. 测试应用功能
3. 重新打包应用程序

## 备注
如需恢复原始版本，请使用备份目录: {self.backup_dir}
"""
        
        with open("brand_replacement_report.md", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 替换报告已生成: brand_replacement_report.md")
    
    def run(self):
        """执行品牌替换"""
        print("🚀 开始品牌替换...")
        print(f"📋 配置文件: {self.config_file}")
        print(f"🎯 目标品牌: {self.config['brand_name']}")
        print()
        
        # 创建备份
        self.create_backup()
        
        # 执行替换操作
        operations = [
            ("更新package.json", self.update_package_json),
            ("更新HTML标题", self.update_html_title),
            ("替换品牌文本", self.replace_brand_text),
            ("创建自定义主题", self.create_custom_theme),
        ]
        
        success_count = 0
        for name, operation in operations:
            print(f"\n🔄 {name}...")
            if operation():
                success_count += 1
            else:
                print(f"❌ {name} 失败")
        
        # 生成报告
        self.generate_report()
        
        print(f"\n🎉 品牌替换完成！成功执行 {success_count}/{len(operations)} 个操作")
        print(f"📦 备份位置: {self.backup_dir}")
        print("📊 详细报告: brand_replacement_report.md")
        
        if success_count == len(operations):
            print("\n✅ 所有操作成功完成！")
            print("🔄 请重新打包应用程序: asar pack extracted_app resources\\app.asar")
        else:
            print(f"\n⚠️ 部分操作失败，请检查错误信息")

def main():
    """主函数"""
    print("🎨 AI助手软件品牌重塑工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        config_file = sys.argv[1]
    else:
        config_file = "brand_config.json"
    
    tool = BrandReplacementTool(config_file)
    tool.run()

if __name__ == "__main__":
    main()
