#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复提供商配置冲突问题
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def fix_provider_name_conflict():
    """修复提供商名称冲突问题"""
    print_header("修复提供商名称冲突问题")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 找到providerList方法中的冲突检查逻辑
        old_conflict_check = 'r[c]||o.push({name:c,id:-1,enable:l.enable!==!1,type:l.type,baseUrl:l.base_url,models:l.models||[],isCustom:!0,channelType:-1,apiKey:l.api_key||"",model:l.model||"auto",maxTokens:l.max_tokens||4096})'
        
        # 修改为：给本地配置添加前缀，避免名称冲突
        new_conflict_check = '''(r[c] ? 
          o.push({name:`Local-${c}`,id:-1,enable:l.enable!==!1,type:l.type,baseUrl:l.base_url,models:l.models||[],isCustom:!0,channelType:-1,apiKey:l.api_key||"",model:l.model||"auto",maxTokens:l.max_tokens||4096}) :
          o.push({name:c,id:-1,enable:l.enable!==!1,type:l.type,baseUrl:l.base_url,models:l.models||[],isCustom:!0,channelType:-1,apiKey:l.api_key||"",model:l.model||"auto",maxTokens:l.max_tokens||4096}))'''
        
        if old_conflict_check in content:
            content = content.replace(old_conflict_check, new_conflict_check)
            print_success("已修复提供商名称冲突逻辑")
        else:
            print_warning("未找到预期的冲突检查代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复名称冲突失败: {e}")
        return False

def fix_custom_provider_creation():
    """修复自定义提供商创建逻辑"""
    print_header("修复自定义提供商创建逻辑")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找providerCreateOne方法中的插入逻辑
        old_insert_pattern = 'this.#e.stmt.providerInsertOne.run(newId, 1, e, providerType, t, 1, i || "auto", r || 8192);'
        
        # 修改为：确保API密钥也被保存
        new_insert_pattern = '''// 保存完整的提供商配置，包括API密钥和基础URL
  this.#e.stmt.providerInsertOne.run(newId, 1, e, providerType, t, 1, i || "auto", r || 8192);
  
  // 额外保存API密钥和基础URL到meta表，用于后续使用
  this.#e.prepare("INSERT OR REPLACE INTO meta (field, value) VALUES (?, ?)").run(
    `custom_provider_${newId}_config`, 
    JSON.stringify({
      apiKey: s,
      baseUrl: baseUrl,
      name: e,
      type: providerType,
      channelType: t
    })
  );'''
        
        if old_insert_pattern in content:
            content = content.replace(old_insert_pattern, new_insert_pattern)
            print_success("已修复自定义提供商创建逻辑")
        else:
            print_warning("未找到预期的提供商插入代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复自定义提供商创建失败: {e}")
        return False

def fix_provider_config_loading():
    """修复提供商配置加载逻辑"""
    print_header("修复提供商配置加载逻辑")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在taskUpdateProviderById方法中添加从meta表加载配置的逻辑
        old_provider_config = 'n={...n,max_tokens:r.maxTokens||1024,base_url:r.baseUrl||"",baseUrl:r.baseUrl||"",api_key:r.apiKey||"",api_base:r.baseUrl||""}'
        
        new_provider_config = '''// 尝试从meta表加载完整的自定义提供商配置
        const customConfigMeta = this.#e.prepare("SELECT value FROM meta WHERE field = ?").get(`custom_provider_${s}_config`);
        let customConfig = {};
        if (customConfigMeta) {
          try {
            customConfig = JSON.parse(customConfigMeta.value);
          } catch (e) {
            console.warn("[DEBUG] Failed to parse custom provider config:", e);
          }
        }
        
        n={
          ...n,
          max_tokens:r.maxTokens||1024,
          base_url: customConfig.baseUrl || r.baseUrl || "",
          baseUrl: customConfig.baseUrl || r.baseUrl || "",
          api_key: customConfig.apiKey || r.apiKey || "",
          api_base: customConfig.baseUrl || r.baseUrl || "",
          type: customConfig.type || r.type || "openai"
        }'''
        
        if old_provider_config in content:
            content = content.replace(old_provider_config, new_provider_config)
            print_success("已修复提供商配置加载逻辑")
        else:
            print_warning("未找到预期的提供商配置代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复配置加载失败: {e}")
        return False

def create_conflict_resolution_guide():
    """创建冲突解决指南"""
    print_header("创建冲突解决指南")
    
    guide_content = '''# 提供商配置冲突解决指南

## 🎯 问题说明

当您同时有本地TOML配置和自定义添加的同名提供商时，会出现配置冲突。

## 🔧 解决方案

### 方案1：使用不同的名称（推荐）

**本地TOML配置：**
```toml
[llm.aihubmix]
type = "openai"
api_key = "your-api-key"
base_url = "https://api.aihubmix.com/v1"
```

**自定义配置：**
- 名称：`My-AIHUBMIX` 或 `AIHUBMIX-Custom`
- 提供商：aihubmix
- API地址：https://api.aihubmix.com/v1

### 方案2：只使用一种配置方式

**选择A：只用本地TOML配置**
- 删除自定义添加的aihubmix模型
- 继续使用本地TOML配置（已验证工作正常）

**选择B：只用自定义配置**
- 从TOML文件中删除aihubmix配置
- 使用界面添加自定义aihubmix模型

## 🧪 测试步骤

### 如果选择方案1（推荐）：

1. **保持本地TOML配置不变**
2. **添加自定义模型时使用不同名称**：
   - 名称：`AIHUBMIX-Custom`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：您的密钥

3. **验证结果**：
   - 应该看到两个aihubmix模型：
     - `aihubmix`（本地配置）
     - `AIHUBMIX-Custom`（自定义配置）
   - 两个都应该能正常工作

### 如果选择方案2A（只用本地配置）：

1. **删除自定义aihubmix模型**
2. **继续使用本地TOML配置**
3. **验证本地配置正常工作**

### 如果选择方案2B（只用自定义配置）：

1. **编辑本地TOML文件，删除aihubmix部分**
2. **重启软件**
3. **添加自定义aihubmix模型**
4. **验证自定义配置正常工作**

## 🔍 故障排除

如果仍有问题：

1. **检查软件日志**中的[DEBUG]输出
2. **确认API密钥正确**
3. **确认网络连接正常**
4. **尝试重启软件**

## 💡 最佳实践

1. **避免同名配置**：本地和自定义使用不同名称
2. **统一管理**：选择一种配置方式并坚持使用
3. **定期备份**：重要配置要备份
4. **测试验证**：配置后及时测试功能
'''
    
    try:
        with open("提供商配置冲突解决指南.md", "w", encoding="utf-8") as f:
            f.write(guide_content)
        print_success("冲突解决指南已创建: 提供商配置冲突解决指南.md")
        return True
    except Exception as e:
        print_error(f"创建指南失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 提供商配置冲突修复")
    print("=" * 60)
    print("本脚本将修复本地TOML配置与自定义配置的冲突问题")
    print("=" * 60)
    
    results = []
    
    # 修复名称冲突
    conflict_ok = fix_provider_name_conflict()
    results.append(("提供商名称冲突修复", conflict_ok))
    
    # 修复自定义提供商创建
    creation_ok = fix_custom_provider_creation()
    results.append(("自定义提供商创建修复", creation_ok))
    
    # 修复配置加载
    loading_ok = fix_provider_config_loading()
    results.append(("提供商配置加载修复", loading_ok))
    
    # 创建解决指南
    guide_ok = create_conflict_resolution_guide()
    results.append(("冲突解决指南创建", guide_ok))
    
    # 总结结果
    print_header("修复结果总结")
    
    all_passed = True
    for test_name, passed in results:
        if passed:
            print_success(f"{test_name}: 成功")
        else:
            print_error(f"{test_name}: 失败")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 冲突修复完成！")
        print("\n🚀 下一步:")
        print("   1. 重新打包应用: asar pack extracted_app resources\\app.asar")
        print("   2. 重启AI助手软件")
        print("   3. 查看指南: 提供商配置冲突解决指南.md")
        print("   4. 选择合适的解决方案进行配置")
        
    else:
        print("❌ 部分修复失败，请检查错误信息")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 修复过程中发生错误: {e}")
        sys.exit(1)
