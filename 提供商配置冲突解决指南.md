# 提供商配置冲突解决指南

## 🎯 问题说明

当您同时有本地TOML配置和自定义添加的同名提供商时，会出现配置冲突。

## 🔧 解决方案

### 方案1：使用不同的名称（推荐）

**本地TOML配置：**
```toml
[llm.aihubmix]
type = "openai"
api_key = "your-api-key"
base_url = "https://api.aihubmix.com/v1"
```

**自定义配置：**
- 名称：`My-AIHUBMIX` 或 `AIHUBMIX-Custom`
- 提供商：aihubmix
- API地址：https://api.aihubmix.com/v1

### 方案2：只使用一种配置方式

**选择A：只用本地TOML配置**
- 删除自定义添加的aihubmix模型
- 继续使用本地TOML配置（已验证工作正常）

**选择B：只用自定义配置**
- 从TOML文件中删除aihubmix配置
- 使用界面添加自定义aihubmix模型

## 🧪 测试步骤

### 如果选择方案1（推荐）：

1. **保持本地TOML配置不变**
2. **添加自定义模型时使用不同名称**：
   - 名称：`AIHUBMIX-Custom`
   - 提供商：aihubmix
   - API地址：https://api.aihubmix.com/v1
   - API密钥：您的密钥

3. **验证结果**：
   - 应该看到两个aihubmix模型：
     - `aihubmix`（本地配置）
     - `AIHUBMIX-Custom`（自定义配置）
   - 两个都应该能正常工作

### 如果选择方案2A（只用本地配置）：

1. **删除自定义aihubmix模型**
2. **继续使用本地TOML配置**
3. **验证本地配置正常工作**

### 如果选择方案2B（只用自定义配置）：

1. **编辑本地TOML文件，删除aihubmix部分**
2. **重启软件**
3. **添加自定义aihubmix模型**
4. **验证自定义配置正常工作**

## 🔍 故障排除

如果仍有问题：

1. **检查软件日志**中的[DEBUG]输出
2. **确认API密钥正确**
3. **确认网络连接正常**
4. **尝试重启软件**

## 💡 最佳实践

1. **避免同名配置**：本地和自定义使用不同名称
2. **统一管理**：选择一种配置方式并坚持使用
3. **定期备份**：重要配置要备份
4. **测试验证**：配置后及时测试功能
