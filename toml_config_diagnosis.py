#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI助手软件TOML配置文件诊断和修复
"""

import subprocess
import time
import sys
import os
import json
import toml
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def find_toml_config_files():
    """查找TOML配置文件"""
    print_header("查找TOML配置文件")
    
    # 可能的TOML配置文件位置
    possible_locations = [
        # 应用程序目录
        os.path.join(os.getcwd(), "config.toml"),
        os.path.join(os.getcwd(), "models.toml"),
        os.path.join(os.getcwd(), "providers.toml"),
        os.path.join(os.getcwd(), "aipy.toml"),
        
        # 用户配置目录
        os.path.join(os.path.expanduser("~"), ".aipyapp", "config.toml"),
        os.path.join(os.path.expanduser("~"), ".aipyapp", "models.toml"),
        os.path.join(os.path.expanduser("~"), ".aipyapp", "providers.toml"),
        
        # AppData目录
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "config.toml"),
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "models.toml"),
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "providers.toml"),
        
        # Local AppData目录
        os.path.join(os.path.expanduser("~"), "AppData", "Local", "aipy-pro", "config.toml"),
        os.path.join(os.path.expanduser("~"), "AppData", "Local", "aipy-pro", "models.toml"),
        
        # 软件安装目录的config子目录
        os.path.join(os.getcwd(), "config", "config.toml"),
        os.path.join(os.getcwd(), "config", "models.toml"),
        os.path.join(os.getcwd(), "config", "providers.toml"),
    ]
    
    found_files = []
    
    for location in possible_locations:
        if os.path.exists(location):
            found_files.append(location)
            print_success(f"找到TOML文件: {location}")
    
    # 递归搜索当前目录和用户目录中的所有.toml文件
    search_dirs = [
        os.getcwd(),
        os.path.join(os.path.expanduser("~"), ".aipyapp"),
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro"),
        os.path.join(os.path.expanduser("~"), "AppData", "Local", "aipy-pro"),
    ]
    
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            print_info(f"搜索目录: {search_dir}")
            for root, dirs, files in os.walk(search_dir):
                for file in files:
                    if file.endswith('.toml'):
                        toml_path = os.path.join(root, file)
                        if toml_path not in found_files:
                            found_files.append(toml_path)
                            print_success(f"发现TOML文件: {toml_path}")
    
    if not found_files:
        print_warning("未找到任何TOML配置文件")
    
    return found_files

def analyze_toml_file(file_path):
    """分析TOML配置文件内容"""
    print_header(f"分析TOML文件: {os.path.basename(file_path)}")
    print_info(f"文件路径: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print_info(f"文件大小: {len(content)} 字符")
        
        # 尝试解析TOML内容
        try:
            toml_data = toml.loads(content)
            print_success("TOML文件格式正确")
            
            # 分析内容结构
            print_info("TOML文件结构:")
            for key, value in toml_data.items():
                if isinstance(value, dict):
                    print(f"   [{key}] - 包含 {len(value)} 个子项")
                    for subkey in value.keys():
                        print(f"     - {subkey}")
                elif isinstance(value, list):
                    print(f"   {key} - 数组，包含 {len(value)} 个元素")
                else:
                    print(f"   {key} = {value}")
            
            # 查找模型提供商相关配置
            provider_keys = ['providers', 'models', 'llm', 'api', 'endpoints']
            found_providers = []
            
            for key in provider_keys:
                if key in toml_data:
                    found_providers.append(key)
                    print_success(f"找到提供商配置节: [{key}]")
                    
                    if isinstance(toml_data[key], dict):
                        for provider_name, provider_config in toml_data[key].items():
                            print_info(f"   提供商: {provider_name}")
                            if isinstance(provider_config, dict):
                                for config_key, config_value in provider_config.items():
                                    if config_key.lower() in ['url', 'api_url', 'base_url', 'endpoint']:
                                        print_info(f"     API地址: {config_value}")
                                    elif config_key.lower() in ['key', 'api_key', 'token']:
                                        print_info(f"     API密钥: {'*' * 10}")
                                    elif config_key.lower() in ['model', 'models']:
                                        print_info(f"     模型: {config_value}")
            
            if not found_providers:
                print_warning("未找到提供商配置节")
            
            return toml_data
            
        except toml.TomlDecodeError as e:
            print_error(f"TOML文件格式错误: {e}")
            print_info("文件内容预览:")
            lines = content.split('\n')
            for i, line in enumerate(lines[:10], 1):
                print(f"   {i:2d}: {line}")
            if len(lines) > 10:
                print(f"   ... (还有 {len(lines) - 10} 行)")
            return None
            
    except Exception as e:
        print_error(f"读取TOML文件失败: {e}")
        return None

def check_toml_loading_code():
    """检查软件中TOML文件加载的代码"""
    print_header("检查TOML文件加载代码")
    
    # 检查主要的JavaScript文件
    js_files = [
        "extracted_app/out/main/index.js",
        "extracted_app/out/renderer/assets/index-DVeT8TLX.js"
    ]
    
    toml_related_keywords = [
        "toml", "config.toml", "models.toml", "providers.toml",
        "readFileSync", "loadConfig", "parseToml"
    ]
    
    for js_file in js_files:
        if os.path.exists(js_file):
            print_info(f"检查文件: {js_file}")
            
            try:
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                found_keywords = []
                for keyword in toml_related_keywords:
                    if keyword.lower() in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print_success(f"找到TOML相关代码: {found_keywords}")
                else:
                    print_warning("未找到TOML相关代码")
                    
            except Exception as e:
                print_error(f"检查{js_file}失败: {e}")
        else:
            print_warning(f"文件不存在: {js_file}")

def create_sample_toml_config():
    """创建示例TOML配置文件"""
    print_header("创建示例TOML配置文件")
    
    sample_config = """
# AI助手软件本地模型提供商配置
# 此文件用于配置本地模型提供商

[providers]

[providers.openai]
name = "OpenAI"
type = "openai"
api_url = "https://api.openai.com/v1"
api_key = "your-openai-api-key"
models = ["gpt-3.5-turbo", "gpt-4"]
enabled = true

[providers.aihubmix]
name = "AIHUBMIX"
type = "openai"
api_url = "https://api.aihubmix.com/v1"
api_key = "your-aihubmix-api-key"
models = ["gpt-3.5-turbo", "gpt-4"]
enabled = true

[providers.claude]
name = "Claude"
type = "anthropic"
api_url = "https://api.anthropic.com/v1"
api_key = "your-claude-api-key"
models = ["claude-3-sonnet", "claude-3-opus"]
enabled = true

[providers.ollama]
name = "OLLAMA"
type = "ollama"
api_url = "http://localhost:11434"
api_key = ""
models = ["llama2", "codellama"]
enabled = true

[providers.deepseek]
name = "DeepSeek"
type = "openai"
api_url = "https://api.deepseek.com/v1"
api_key = "your-deepseek-api-key"
models = ["deepseek-chat", "deepseek-coder"]
enabled = true

# 全局设置
[settings]
default_provider = "aihubmix"
timeout = 30
max_tokens = 8192
"""
    
    # 可能的配置文件位置
    config_locations = [
        "config.toml",
        "models.toml",
        "providers.toml",
        os.path.join(os.path.expanduser("~"), ".aipyapp", "config.toml"),
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "config.toml"),
    ]
    
    for location in config_locations:
        if not os.path.exists(location):
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(location), exist_ok=True)
                
                with open(location, 'w', encoding='utf-8') as f:
                    f.write(sample_config)
                
                print_success(f"创建示例配置文件: {location}")
                break
                
            except Exception as e:
                print_warning(f"无法创建配置文件 {location}: {e}")
                continue
    else:
        print_error("无法创建示例配置文件")

def main():
    """主函数"""
    print("🔧 AI助手软件TOML配置文件诊断")
    print("=" * 60)
    print("本脚本将诊断本地TOML配置文件的问题:")
    print("1. 查找TOML配置文件位置")
    print("2. 分析TOML文件内容")
    print("3. 检查软件中的TOML加载代码")
    print("4. 创建示例配置文件（如果需要）")
    print("=" * 60)
    
    # 1. 查找TOML配置文件
    toml_files = find_toml_config_files()
    
    # 2. 分析找到的TOML文件
    valid_configs = []
    for toml_file in toml_files:
        config_data = analyze_toml_file(toml_file)
        if config_data:
            valid_configs.append((toml_file, config_data))
    
    # 3. 检查TOML加载代码
    check_toml_loading_code()
    
    # 4. 如果没有找到有效配置，创建示例
    if not valid_configs:
        print_warning("未找到有效的TOML配置文件")
        create_sample_toml_config()
    
    # 总结
    print_header("诊断结果总结")
    
    if toml_files:
        print_success(f"找到 {len(toml_files)} 个TOML文件")
        for toml_file in toml_files:
            print_info(f"   - {toml_file}")
    else:
        print_error("未找到任何TOML配置文件")
    
    if valid_configs:
        print_success(f"找到 {len(valid_configs)} 个有效的配置文件")
        for config_file, config_data in valid_configs:
            print_info(f"   - {config_file}")
    else:
        print_warning("未找到有效的配置文件")
    
    print("\n" + "=" * 60)
    print("🚀 下一步建议:")
    
    if valid_configs:
        print("   • TOML配置文件存在且格式正确")
        print("   • 需要检查软件是否正确加载这些配置")
        print("   • 可能需要修复软件中的TOML读取逻辑")
    else:
        print("   • 需要创建或恢复TOML配置文件")
        print("   • 检查配置文件的位置是否正确")
        print("   • 验证配置文件的格式和内容")
    
    print("   • 重启AI助手软件测试配置加载")
    print("   • 检查软件日志获取详细错误信息")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 诊断被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 诊断过程中发生错误: {e}")
        sys.exit(1)
