#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import os
import sys
import uuid
import json
import time
import traceback
import sqlite3
from dataclasses import dataclass
from typing import Optional, Dict, Any
from pathlib import Path

from aipyapp.aipy.mcp_tool import MCPToolManager
from aipyapp.aipy.cache import get_default_cache, cache_key
from aipyapp.aipy.plugin import event_bus
from aipyapp.aipy.llm import CLIENTS, Client, ClientManager
from aipyapp.aipy.task import Task
from aipyapp.aipy.diagnose import Diagnose
from aipyapp.aipy.multimodal import MMContent, is_text_file
from aipyapp.aipy import prompt

from common import EventType, SystemEventType, Status, NoOpProxy, print_event

stderr = sys.stderr

class AttrDict(dict):
    def __init__(self, *args, **kwargs):
        super(AttrDict, self).__init__(*args, **kwargs)
        for key, value in self.items():
            if isinstance(value, dict):
                self[key] = AttrDict(value)

    def __getattr__(self, name):
        try:
            return self[name]
        except KeyError:
            raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

class DummyConsole:
    def __getattribute__(self, name):
        return lambda *args, **kwargs: None

@dataclass
class TaskManger:
    envs: Dict[str, str] = dict
    gui: Optional[bool] = False
    settings: Optional[Dict[str, Any]] = None
    current: Optional[Client] = None
    cwd: Optional[str] = Path(os.getcwd())
    console = DummyConsole()


class AipyClient(Client):
    def __init__(self, config):
        manager = ClientManager(AttrDict({"llm": {"trustoken": {**config, "default": True}}}))
        super().__init__(manager)
        self.client_type = "trust"

    def use(self, config):
        if self.current and self.current._params:
            params = self.current._params
        client_type = config.get("type", "trust")
        Client = CLIENTS.get(client_type)
        if not Client:
            return False

        model = config.pop("model")
        provider_id = config.pop("provider_id") or 0

        self.current = Client(config)

        if params and params.get("extra_headers"):
            self.current._params["extra_headers"] = params["extra_headers"]

        # 彻底修复502错误：对于aihubmix等第三方API，完全跳过x-custom-conf-id逻辑
        # 检查所有可能的URL字段名
        base_url = config.get("base_url", "") or config.get("baseUrl", "") or config.get("api_base", "")
        print(f"[DEBUG] provider_id={provider_id}, base_url={base_url}")
        print(f"[DEBUG] config keys: {list(config.keys())}")

        # 检查是否为aihubmix API
        is_aihubmix = base_url and "aihubmix.com" in base_url

        if provider_id > 0 and not is_aihubmix:
            # 只对非aihubmix的API添加x-custom-conf-id
            extra_headers = self.current._params.get("extra_headers", {})
            extra_headers["x-custom-conf-id"] = str(provider_id)
            self.current._params["extra_headers"] = extra_headers
            print(f"[DEBUG] Added x-custom-conf-id: {provider_id}")
        else:
            if is_aihubmix:
                print(f"[DEBUG] Completely skipped x-custom-conf-id for aihubmix (URL: {base_url})")
            else:
                print(f"[DEBUG] Skipped x-custom-conf-id (provider_id={provider_id})")

        self.current._model = model
        self.client_type = client_type

        return self.current

class AipyTask(Task):
    MAX_ROUNDS = 16

    def __init__(self, task_id = uuid.uuid4().hex, envs = {}, settings = {}, provider = {}):
        manager = TaskManger(envs=envs, gui=False, settings=settings)
        manager.cwd = Path(settings.get("workdir"))
        super().__init__(manager)
        self.code_blocks.console = manager.console
        self.code_blocks.log = NoOpProxy(self.code_blocks.log)
        self.runtime.console = manager.console
        self.console = manager.console
        self.cwd = manager.cwd
        self.task_id = task_id
        self.client = AipyClient(provider)
        self.diagnose = Diagnose.create(settings)
        self.cid = ""

    def box(self, *args, **kwargs):
        pass

    def process_reply(self, markdown):
        parse_mcp = self.mcp is not None
        ret = self.code_blocks.parse(markdown, parse_mcp=parse_mcp)
        if not ret:
            return None

        json_str = json.dumps(ret, ensure_ascii=False, indent=2, default=str)

        if 'call_tool' in ret:
            # 有可能MCP调用会按照代码格式返回，这种情况下存在exec_blocks和call_tool两个键,也可能会有erros字段
            # 优先处理call_tool
            return self.process_mcp_reply(ret['call_tool'])

        errors = ret.get('errors')
        if errors:
            event_bus('result', errors)
            feed_back = f"# 消息解析错误\n{json_str}"
            ret = self.chat(feed_back)
        elif 'exec_blocks' in ret:
            ret = self.process_code_reply(ret['exec_blocks'])
        else:
            event_bus('result', ret)
            ret = None
        return ret

    def process_code_reply(self, exec_blocks):
        results = []
        for block in exec_blocks:
            event_bus('exec', block)
            result = self.runner(block)
            results.append(result)
            event_bus('result', result, block=block)
        msg = prompt.get_results_prompt(results)
        try:
            feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        except:
            feed_back = str(msg)
        return self.chat(feed_back)

    def process_mcp_reply(self, json_content):
        """处理 MCP 工具调用的回复"""
        call_tool = json.loads(json_content)
        event = {
            'id': uuid.uuid4().hex,
            'type': 'mcp',
            "tool": call_tool['name'],
            'arguments': call_tool.get('arguments', {}),
            'done': False,
        }
        event_bus('tool_call', event)
        result = self.mcp.call_tool(call_tool['name'], call_tool.get('arguments', {}))
        event["result"] = result
        event["done"] = True
        event_bus('tool_call', event)

        msg = prompt.get_mcp_result_prompt(result)
        try:
            feed_back = json.dumps(msg, ensure_ascii=False, default=str)
        except:
            feed_back = str(msg)
        feedback_response = self.chat(feed_back)
        return feedback_response

    def chat(self, instruction, *, system_prompt=None):
        if self.is_stopped():
            return ""
        print_event(
            event_type=EventType.SYSTEM,
            content={"type": SystemEventType.STATUS, "value": Status.THINKING})
        self.cid = uuid.uuid4().hex
        return super().chat(instruction, system_prompt=system_prompt)

    def save(self, *args, **kwargs):
        pass


class AipyMCP(MCPToolManager):
    def __init__(self, config_path, tt_api_key):
        super().__init__(config_path, tt_api_key)

    def list_tools(self, mcp_type="user", force_load=False):
        if force_load:
            return super().list_tools(mcp_type, force_load)

        ret = {}
        cache = get_default_cache()
        with cache._lock:
            with sqlite3.connect(cache.db_path) as conn:
                cursor = conn.execute('SELECT key, value FROM cache')
                for (key, value) in cursor.fetchall():
                    key_parts = key.split(":")
                    if len(key_parts) < 3 or key_parts[0] != "mcp_tool":
                        continue
                    key = key_parts[1]
                    if mcp_type == "user" and (key not in self.user_mcp or key.startswith("Trustoken-")):
                        continue
                    elif mcp_type == "sys" and (key not in self.sys_mcp or not key.startswith("Trustoken-")):
                        continue
                    try:
                        ret[key] = json.loads(value)
                        self._tools_dict[key_parts[1]] = ret[key]
                    except:
                        traceback.print_exc(file=stderr)
                        continue
                if mcp_type == "sys":
                    if not ret.get("Trustoken-search"):
                        tools = [{"name": "get_my_ip", "title": None, "description": "获取当前客户端的公网IP地址以及IP的地理位置信息", "inputSchema": {"properties": {}, "type": "object"}, "outputSchema": None, "annotations": {"title": None, "readOnlyHint": False, "destructiveHint": True, "idempotentHint": False, "openWorldHint": True}, "meta": None, "server": "Trustoken-search", "id": "Trustoken-search.get_my_ip"}, {"name": "search", "title": None, "description": "联网搜索服务，可以用于检索新闻和模型不知道的最新信息", "inputSchema": {"properties": {"queryString": {"description": "搜索的关键词，不支持'site：'语法", "type": "string"}, "timeRange": {"default": "NoLimit", "description": "The time range for the search, e.g., 'OneDay'", "enum": ["OneDay", "OneWeek", "OneMonth", "OneYear", "NoLimit"], "type": "string"}}, "type": "object"}, "outputSchema": None, "annotations": {"title": None, "readOnlyHint": False, "destructiveHint": True, "idempotentHint": False, "openWorldHint": True}, "meta": None, "server": "Trustoken-search", "id": "Trustoken-search.search"}]
                        conn.execute(
                            'INSERT OR IGNORE INTO cache (key, value, expire_time, created_time) VALUES (?, ?, ?, ?)',
                            (
                                f"mcp_tool:Trustoken-search:{cache_key(self.sys_mcp.get('Trustoken-search'))}",
                                json.dumps(tools, ensure_ascii=False),
                                time.time() + 60 * 60 * 24 * 30,
                                time.time()
                            )
                        )
                        ret["Trustoken-search"] = tools
                    if not ret.get("Trustoken-map"):
                        tools = [{"name": "maps_direction_bicycling", "title": None, "description": "骑行路径规划用于规划骑行通勤方案，规划时会考虑天桥、单行线、封路等情况。最大支持 500km 的骑行路线规划", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_bicycling"}, {"name": "maps_direction_driving", "title": None, "description": "驾车路径规划 API 可以根据用户起终点经纬度坐标规划以小客车、轿车通勤出行的方案，并且返回通勤方案的数据。", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_driving"}, {"name": "maps_direction_transit_integrated", "title": None, "description": "根据用户起终点经纬度坐标规划综合各类公共（火车、公交、地铁）交通方式的通勤方案，并且返回通勤方案的数据，跨城场景下必须传起点城市与终点城市", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经纬度，坐标格式为：经度，纬度"}, "city": {"type": "string", "description": "公共交通规划起点城市"}, "cityd": {"type": "string", "description": "公共交通规划终点城市"}}, "required": ["origin", "destination", "city", "cityd"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_transit_integrated"}, {"name": "maps_direction_walking", "title": None, "description": "根据输入起点终点经纬度坐标规划100km 以内的步行通勤方案，并且返回通勤方案的数据", "inputSchema": {"type": "object", "properties": {"origin": {"type": "string", "description": "出发点经度，纬度，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "目的地经度，纬度，坐标格式为：经度，纬度"}}, "required": ["origin", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_direction_walking"}, {"name": "maps_distance", "title": None, "description": "测量两个经纬度坐标之间的距离,支持驾车、步行以及球面距离测量", "inputSchema": {"type": "object", "properties": {"origins": {"type": "string", "description": "起点经度，纬度，可以传多个坐标，使用竖线隔离，比如120,30|120,31，坐标格式为：经度，纬度"}, "destination": {"type": "string", "description": "终点经度，纬度，坐标格式为：经度，纬度"}, "type": {"type": "string", "description": "距离测量类型,1代表驾车距离测量，0代表直线距离测量，3步行距离测量"}}, "required": ["origins", "destination"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_distance"}, {"name": "maps_geo", "title": None, "description": "将详细的结构化地址转换为经纬度坐标。支持对地标性名胜景区、建筑物名称解析为经纬度坐标", "inputSchema": {"type": "object", "properties": {"address": {"type": "string", "description": "待解析的结构化地址信息"}, "city": {"type": "string", "description": "指定查询的城市"}}, "required": ["address"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_geo"}, {"name": "maps_regeocode", "title": None, "description": "将一个高德经纬度坐标转换为行政区划地址信息", "inputSchema": {"type": "object", "properties": {"location": {"type": "string", "description": "经纬度"}}, "required": ["location"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_regeocode"}, {"name": "maps_ip_location", "title": None, "description": "IP 定位根据用户输入的 IP 地址，定位 IP 的所在位置", "inputSchema": {"type": "object", "properties": {"ip": {"type": "string", "description": "IP地址"}}, "required": ["ip"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_ip_location"}, {"name": "maps_schema_personal_map", "title": None, "description": "用于行程规划结果在高德地图展示。将行程规划位置点按照行程顺序填入lineList，返回结果为高德地图打开的URI链接，该结果不需总结，直接返回！", "inputSchema": {"type": "object", "properties": {"orgName": {"type": "string", "description": "行程规划地图小程序名称"}, "lineList": {"type": "array", "description": "行程列表", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "行程名称描述（按行程顺序）"}, "pointInfoList": {"type": "array", "description": "行程目标位置点描述", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "行程目标位置点名称"}, "lon": {"type": "number", "description": "行程目标位置点经度"}, "lat": {"type": "number", "description": "行程目标位置点纬度"}, "poiId": {"type": "string", "description": "行程目标位置点POIID"}}, "required": ["name", "lon", "lat", "poiId"]}}}, "required": ["title", "pointInfoList"]}}}, "required": ["orgName", "lineList"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_personal_map"}, {"name": "maps_around_search", "title": None, "description": "周边搜，根据用户传入关键词以及坐标location，搜索出radius半径范围的POI", "inputSchema": {"type": "object", "properties": {"keywords": {"type": "string", "description": "搜索关键词"}, "location": {"type": "string", "description": "中心点经度纬度"}, "radius": {"type": "string", "description": "搜索半径"}}, "required": ["keywords", "location"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_around_search"}, {"name": "maps_search_detail", "title": None, "description": "查询关键词搜或者周边搜获取到的POI ID的详细信息", "inputSchema": {"type": "object", "properties": {"id": {"type": "string", "description": "关键词搜或者周边搜获取到的POI ID"}}, "required": ["id"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_search_detail"}, {"name": "maps_text_search", "title": None, "description": "关键字搜索 API 根据用户输入的关键字进行 POI 搜索，并返回相关的信息", "inputSchema": {"type": "object", "properties": {"keywords": {"type": "string", "description": "查询关键字"}, "city": {"type": "string", "description": "查询城市"}, "citylimit": {"type": "boolean", "default": False, "description": "是否限制城市范围内搜索，默认不限制"}}, "required": ["keywords"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_text_search"}, {"name": "maps_schema_navi", "title": None, "description": " Schema唤醒客户端-导航页面，用于根据用户输入终点信息，返回一个拼装好的客户端唤醒URI，用户点击该URI即可唤起对应的客户端APP。唤起客户端后，会自动跳转到导航页面。", "inputSchema": {"type": "object", "properties": {"lon": {"type": "string", "description": "终点经度"}, "lat": {"type": "string", "description": "终点纬度"}}, "required": ["lon", "lat"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_navi"}, {"name": "maps_schema_take_taxi", "title": None, "description": "根据用户输入的起点和终点信息，返回一个拼装好的客户端唤醒URI，直接唤起高德地图进行打车。直接展示生成的链接，不需要总结", "inputSchema": {"type": "object", "properties": {"slon": {"type": "string", "description": "起点经度"}, "slat": {"type": "string", "description": "起点纬度"}, "sname": {"type": "string", "description": "起点名称"}, "dlon": {"type": "string", "description": "终点经度"}, "dlat": {"type": "string", "description": "终点纬度"}, "dname": {"type": "string", "description": "终点名称"}}, "required": ["dlon", "dlat", "dname"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_schema_take_taxi"}, {"name": "maps_weather", "title": None, "description": "根据城市名称或者标准adcode查询指定城市的天气", "inputSchema": {"type": "object", "properties": {"city": {"type": "string", "description": "城市名称或者adcode"}}, "required": ["city"]}, "outputSchema": None, "annotations": None, "meta": None, "server": "Trustoken-map", "id": "Trustoken-map.maps_weather"}]
                        conn.execute(
                            'INSERT OR IGNORE INTO cache (key, value, expire_time, created_time) VALUES (?, ?, ?, ?)',
                            (
                                f"mcp_tool:Trustoken-map:{cache_key(self.sys_mcp.get('Trustoken-map'))}",
                                json.dumps(tools, ensure_ascii=False),
                                time.time() + 60 * 60 * 24 * 30,
                                time.time()
                            )
                        )
                        ret["Trustoken-map"] = tools

        return ret

def mmcontent_from_string(self, text: str, base_path: Path = None) -> list:
    """
    从输入字符串解析多模态内容，支持@file.pdf、@image.jpg等文件引用，返回MMContent对象
    支持带引号的文件路径，如 @"path with spaces.txt"
    """
    # 匹配 @文件路径，支持带引号的路径
    # {"instruction": "@\"/home/<USER>/Downloads/啊 阿斯顿 的撒/asd\\\"\\\"das/2.txt\""}
    parts = re.split(r'(@(?:"(?:[^"\\]*(?:\\.[^"\\]*)*)"|\S+))', text)
    items = []
    for part in parts:
        part = part.strip()
        if not part:
            continue
        if part.startswith('@'):
            file_path = part[1:]
            # 去除文件路径的引号
            if (file_path.startswith('"') and file_path.endswith('"')):
                file_path = json.loads(file_path)
            ext = Path(file_path).suffix.lower()
            file_type = 'image' if ext in {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'} else None
            if base_path:
                p = Path(file_path)
                if not p.is_absolute():
                    file_path = str(base_path / p)

            # 检查文件是否存在，如果不存在则作为普通文本处理
            if not os.path.isfile(file_path):
                items.append({'type': 'text', 'text': part})
                continue

            if not file_type:
                # 判断文本/二进制
                if is_text_file(file_path):
                    file_type = 'document'
                else:
                    file_type = 'file'
            items.append({'type': file_type, 'path': file_path})
        else:
            items.append({'type': 'text', 'text': part})

    return items

MMContent._from_string = mmcontent_from_string


prompt.AIPY_PROMPT = """
# 输出内容格式规范
输出内容必须采用结构化的 Markdown 格式，并符合以下规则：

## 多行代码块标记
1. 代码块必须用一对HTML注释标记包围，格式如下：
   - 代码开始：<!-- Block-Start: {"name": "代码块名称", "version": 数字版本号如1/2/3, "path": "该代码块的可选文件路径"} -->
   - 代码本体：用 Markdown 代码块包裹（如 ```python 或 ```html 等)。
   - 代码结束：<!-- Block-End: { "name": 和Block-Start中的name一致 } -->

2. 多个代码块可以使用同一个name，但版本必须不同。版本最高的代码块会被认为是最新的有效版本。注意：不要在`name` 中包含版本号。

3. `path` 为代码块需要保存为的本地文件路径可以包含目录, 如果是相对路径则默认为相对当前目录或者用户指定目录.

4. 同一个输出消息里可以定义多个代码块。

5. **正确示例：**
<!-- Block-Start: {"name": "abc123", "version": 1, "path": "main.py"} -->
```python
print("hello world")
```
<!-- Block-End: {"name": "abc123"} -->

## 单行命令标记
1. 每次输出中只能包含 **一个** `Cmd-Exec` 标记，用于执行可执行代码块来完成用户的任务：
   - 格式：<!-- Cmd-Exec: {"name": "要执行的代码块 name"} -->
   - 如果不需要执行任何代码，则不要添加 `Cmd-Exec`。
   - 要执行的代码块必需先使用前述多行代码块标记格式单独定义。
   - 如果代码块有多个版本，执行代码块的最新版本。
   - 可以使用 `Cmd-Exec` 执行会话历史中的所有代码块。特别地，如果需要重复执行某个任务，尽量使用 `Cmd-Exec` 执行而不是重复输出代码块。

2. Cmd-Exec 只能用来执行下面列出的代码块类型：
    - Python 代码块：语言类型为 `python` 的代码块。
    - HTML 代码块：语言类型为 `html` 的代码块且代码块必需指定了 `path` 属性。
    - Bash 代码块：语言类型为 `bash` 的代码块且代码块必需指定了 `path` 属性。
    - PowerShell 代码块：语言类型为 `powershell` 的代码块且代码块必需指定了 `path` 属性。
    - AppleScript 代码块：语言类型为 `applescript` 的代码块且代码块必需指定了 `path` 属性。
    - NodeJS 代码块：语言类型为 `javascript` 的代码块且代码块必需指定了 `path` 属性。

3. 下述类型的代码块时应该根据客户端操作系统类型选择：
    - Bash 代码块：仅在 Linux 和 macOS 系统上执行。
    - PowerShell 代码块：仅在 Windows 系统上执行。
    - AppleScript 代码块：仅在 macOS 系统上执行。

4. **正确示例：**
<!-- Cmd-Exec: {"name": "abc123"} -->

## 其它
1. 所有 JSON 内容必须写成**单行紧凑格式**，例如：
   <!-- Block-Start: {"name": "abc123", "path": "main.py", "version": 1} -->

2. 禁止输出代码内容重复的代码块，通过代码块name来引用之前定义过的代码块。

遵循上述规则，生成输出内容。

# 生成Python代码规则
- 确保代码在下述`Python运行环境描述`中描述的运行环境中可以无需修改直接执行
- 实现适当的错误处理，包括但不限于：
  * 文件操作的异常处理
  * 网络请求的超时和连接错误处理
  * 数据处理过程中的类型错误和值错误处理
- 如果需要区分正常和错误信息，可以把错误信息输出到 stderr。
- 不允许执行可能导致 Python 解释器退出的指令，如 exit/quit 等函数，请确保代码中不包含这类操作。
- 用户通过 <attachment filename="path/to/file"> 标记上传的文件，都是文本内容，你可以直接分析和处理，无需写代码读取文件内容。

# Python运行环境描述
在标准 Python 运行环境的基础上额外增加了下述包/模块：
- 一些预装的第三方包
- `aipyapp.runtime` 模块
- 成功执行过的 Python 代码块可以通过 `from blocks import 代码块名` 导入来实现代码重用

生成 Python 代码时可以直接使用这些额外功能。

## 预装的第三方包
下述第三方包可以无需安装直接使用：
- `requests`、`numpy`、`pandas`、`matplotlib`、`seaborn`、`beautifulsoup4`、`charset-normalizer`。

其它第三方包，都必需通过下述 runtime 对象的 install_packages 方法申请安装才能使用。

在使用 matplotlib 时，需要根据系统类型选择和设置合适的中文字体，否则图片里中文会乱码导致无法完成客户任务。
示例代码如下：
```python
import platform

system = platform.system().lower()
font_options = {
    'windows': ['Microsoft YaHei', 'SimHei'],
    'darwin': ['Kai', 'Hei'],
    'linux': ['Noto Sans CJK SC', 'WenQuanYi Micro Hei', 'Source Han Sans SC']
}
```

## `aipyapp.runtime` 模块
通过 `from aipyapp import runtime` 来使用下述方法辅助完成任务。

### `set_state` 方法
- 定义: `set_state(self, success: bool, **kwargs)`
- 参数:
  - success: 布尔值，表示代码块执行是否成功。
  - **kwargs: 状态键值对，类型可以为任意Python基本数据
- 用途：保存当前代码块的执行结果/状态。
- 使用示例：
```python
runtime.set_state(True, data={"name": "John", "age": 30})
runtime.set_state(False, error="Something went wrong")
```

### `get_block_state` 方法
- 用途：获取指定代码块的最新状态值。
- 定义: `get_block_state(self, block_name: str)`
- 参数:
  - block_name: 代码块名称
- 返回值: 状态值，如果未设置则返回空字典，如果代码块未执行或不存在则返回 None。
- 使用示例：
```python
state = runtime.get_block_state("abc123")
```

### `set_persistent_state` 方法
- 定义: `set_persistent_state(self, **kwargs)`
- 参数:
  - **kwargs: 状态键值对，类型可以为任意Python基本数据类型，如字符串/数字/列表/字典等。
- 用途: 设置会话中持久化的状态值。
- 使用示例：
```python
runtime.set_persistent_state(data={"name": "John", "age": 30}) # 保存数据到会话中
```

### `get_persistent_state` 方法
- 定义: `get_persistent_state(key)`
- 参数:
  - key: 状态键名
- 用途: 获取会话中持久化的状态值。不存在时返回 None。
- 使用示例：
```python
data = runtime.get_persistent_state("data")
```

### `get_block_by_name` 方法
- 功能: 获取指定 name 的最新版本的代码块对象
- 定义: `get_block_by_name(code_block_name)`
- 参数: `code_block_name` 为代码块的名称
- 返回值: 代码块对象，如果不存在则返回 None。

返回的代码块对象包含以下属性：
- `name`: 代码块名称
- `version`: 代码块的版本号
- `lang`: 代码块的编程语言
- `code`: 代码块的代码内容
- `path`: 代码块的文件路径（如果之前未指定则为None）

可以修改代码块的 `code` 属性来更新代码内容。

### `install_packages` 方法
- 功能: 申请安装完成任务必需的额外模块
- 定义: install_packages(*packages)
- 参数: 一个或多个 PyPi 包名，如：'httpx', 'requests>=2.25'
- 返回值:True 表示成功, False 表示失败

示例如下：
```python
if runtime.install_packages('httpx', 'requests>=2.25'):
    import httpx
```

### `get_env` 方法
- 功能: 获取代码运行需要的环境变量，如 API-KEY 等。
- 定义: get_env(name, default=None, *, desc=None)
- 参数: 第一个参数为需要获取的环境变量名称，第二个参数为不存在时的默认返回值，第三个可选字符串参数简要描述需要的是什么。
- 返回值: 环境变量值，返回 None 或空字符串表示未找到。

示例如下：
```python
env_name = '环境变量名称'
env_value = runtime.get_env(env_name, "No env", desc='访问API服务需要')
if not env_value:
    print(f"Error: {env_name} is not set", file=sys.stderr)
else:
    print(f"{env_name} is available")
```

# 代码块执行结果反馈
代码块的执行结果会通过JSON格式反馈给你。

每个代码块的执行结果对象都有下述属性：
- `stdout`: 标准输出内容
- `stderr`: 标准错误输出
- `errstr`: 异常信息
- `block_name`: 对应的代码块名称

注意：
- 如果某个属性为空，它不会出现在反馈中。

收到反馈后，结合代码和反馈数据，做出下一步的决策。

## Python 代码块执行结果
还包括以下属性：
- `__state__`: 前述`__state__` 变量的内容
- `traceback`: 异常堆栈信息

## Bash/PowerShell/AppleScript 代码块
还包括下述属性：
- `returncode`: 执行代码块的 subprocess 进程退出码
"""
