#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI助手软件调试启动脚本
"""

import subprocess
import time
import sys
import os
import threading
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def monitor_log_file():
    """监控日志文件"""
    log_paths = [
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "log", "aipy-pro.log"),
        os.path.join(os.path.expanduser("~"), ".aipyapp", "log", "aipy-pro.log"),
        "log/aipy-pro.log",
        "aipy-pro.log"
    ]
    
    log_file = None
    for path in log_paths:
        if os.path.exists(path):
            log_file = path
            break
    
    if log_file:
        print_success(f"找到日志文件: {log_file}")
        try:
            # 获取当前文件大小
            current_size = os.path.getsize(log_file)
            
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(current_size)  # 跳到文件末尾
                
                while True:
                    line = f.readline()
                    if line:
                        # 过滤并高亮我们关心的日志
                        if any(keyword in line for keyword in ['[DEBUG]', 'provider_id', 'aihubmix', '502', 'x-custom-conf-id']):
                            print(f"🔍 LOG: {line.strip()}")
                        elif 'ERROR' in line.upper() or 'WARN' in line.upper():
                            print(f"⚠️ LOG: {line.strip()}")
                    else:
                        time.sleep(0.1)
        except Exception as e:
            print_error(f"监控日志文件失败: {e}")
    else:
        print_warning("未找到日志文件，将监控控制台输出")

def start_app_with_debug():
    """以调试模式启动应用"""
    print_header("启动AI助手软件（调试模式）")
    
    app_path = "AiPyPro.exe"
    if not os.path.exists(app_path):
        print_error(f"软件不存在: {app_path}")
        return None
    
    try:
        # 设置环境变量启用调试
        env = os.environ.copy()
        env['ELECTRON_ENABLE_LOGGING'] = '1'
        env['ELECTRON_LOG_LEVEL'] = 'debug'
        env['DEBUG'] = '*'
        
        print_info("启动软件...")
        print_info("环境变量:")
        print_info("  ELECTRON_ENABLE_LOGGING=1")
        print_info("  ELECTRON_LOG_LEVEL=debug")
        print_info("  DEBUG=*")
        
        # 启动进程
        process = subprocess.Popen(
            [app_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            env=env,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        print_success("软件已启动，PID: " + str(process.pid))
        print_info("正在监控输出...")
        print_info("请在软件中测试自定义aihubmix模型")
        print()
        
        return process
        
    except Exception as e:
        print_error(f"启动软件失败: {e}")
        return None

def monitor_process_output(process):
    """监控进程输出"""
    if not process:
        return
    
    try:
        for line in iter(process.stdout.readline, ''):
            if line:
                line = line.strip()
                # 过滤并高亮重要信息
                if any(keyword in line for keyword in ['[DEBUG]', 'provider_id', 'aihubmix', '502', 'x-custom-conf-id']):
                    print(f"🔍 APP: {line}")
                elif any(keyword in line.upper() for keyword in ['ERROR', 'WARN', 'FAIL']):
                    print(f"⚠️ APP: {line}")
                elif 'LOAD-SETTINGS' in line:
                    print(f"⚙️ APP: {line}")
                # 其他日志只在包含关键词时显示
                elif any(keyword in line for keyword in ['aipy', 'provider', 'llm', 'model']):
                    print(f"📝 APP: {line}")
    except Exception as e:
        print_error(f"监控进程输出失败: {e}")

def main():
    """主函数"""
    print("🔧 AI助手软件调试启动器")
    print("=" * 60)
    print("本脚本将以调试模式启动软件并监控相关日志")
    print("特别关注 provider_id 和 aihubmix 相关的调试信息")
    print("=" * 60)
    
    # 启动应用
    process = start_app_with_debug()
    
    if not process:
        print_error("无法启动应用")
        return
    
    # 启动日志监控线程
    log_thread = threading.Thread(target=monitor_log_file, daemon=True)
    log_thread.start()
    
    print_header("实时日志监控")
    print_info("正在监控以下关键信息:")
    print_info("  • [DEBUG] provider_id 和 base_url 的值")
    print_info("  • x-custom-conf-id 请求头的处理")
    print_info("  • aihubmix 相关的特殊处理")
    print_info("  • 502 错误信息")
    print()
    print_warning("请在软件中:")
    print_warning("  1. 添加自定义 aihubmix 模型")
    print_warning("  2. 发送测试消息")
    print_warning("  3. 观察下方的调试输出")
    print()
    print("按 Ctrl+C 停止监控")
    print("-" * 60)
    
    try:
        # 监控进程输出
        monitor_process_output(process)
        
        # 等待进程结束
        process.wait()
        
    except KeyboardInterrupt:
        print("\n")
        print_warning("用户中断监控")
        try:
            process.terminate()
            process.wait(timeout=5)
            print_success("软件已正常关闭")
        except subprocess.TimeoutExpired:
            process.kill()
            print_warning("软件已强制关闭")
        except Exception as e:
            print_error(f"关闭软件时出错: {e}")
    
    except Exception as e:
        print_error(f"监控过程中出错: {e}")
    
    finally:
        print_header("调试会话结束")
        print_info("如果看到了 [DEBUG] 输出，请检查:")
        print_info("  • provider_id 的值是否 > 0")
        print_info("  • base_url 是否包含 'aihubmix.com'")
        print_info("  • 是否显示 'Skipped x-custom-conf-id for aihubmix'")
        print()
        print_info("如果没有看到 [DEBUG] 输出，可能需要:")
        print_info("  • 确认修改已保存")
        print_info("  • 重新打包应用 (asar pack)")
        print_info("  • 检查 Python 代码是否被正确加载")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 启动被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 启动过程中发生错误: {e}")
        sys.exit(1)
