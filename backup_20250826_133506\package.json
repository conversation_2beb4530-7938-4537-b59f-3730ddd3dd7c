{"name": "aipy-pro", "displayName": "AiPy Pro", "version": "0.8.2", "description": "AiPy Pro - Your Super AI Assistant!", "main": "./out/main/index.js", "author": "AIPY Team", "homepage": "https://aipy.app", "dependencies": {"@braintree/sanitize-url": "^7.1.1", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@modelcontextprotocol/sdk": "^1.17.2", "@std/toml": "npm:@jsr/std__toml@^1.0.7", "@std/yaml": "npm:@jsr/std__yaml@^1.0.8", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-webgl": "^0.18.0", "@xterm/xterm": "^5.5.0", "adm-zip": "^0.5.16", "bunyan": "^1.8.15", "clsx": "^2.1.1", "electron-updater": "^6.6.2", "he": "^1.2.0", "highlight.js": "^11.11.1", "intl-template": "^1.0.0-alpha.5", "lodash": "^4.17.21", "mermaid": "^11.6.0", "mitt": "^3.0.1", "node-machine-id": "^1.1.12", "node-pty": "^1.1.0-beta34", "pkce-challenge": "^5.0.0", "preact": "^10.26.8", "ramda": "^0.31.3", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "rehype-external-links": "^3.0.0", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "undici": "^7.10.0", "virtua": "~0.41.5", "wouter-preact": "^3.7.1", "zod": "^3.25.58"}}