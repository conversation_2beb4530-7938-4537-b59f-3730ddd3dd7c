{"version": 3, "file": "windowsPtyAgent.js", "sourceRoot": "", "sources": ["../src/windowsPtyAgent.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,uBAAyB;AACzB,uBAAyB;AACzB,2BAA6B;AAC7B,+CAAqC;AACrC,2BAA6B;AAE7B,qEAA6D;AAE7D,IAAI,YAA2B,CAAC;AAChC,IAAI,YAA2B,CAAC;AAEhC;;;;GAIG;AACH,IAAM,mBAAmB,GAAG,IAAI,CAAC;AAEjC;;;GAGG;AACH;IAmBE,yBACE,IAAY,EACZ,IAAuB,EACvB,GAAa,EACb,GAAW,EACX,IAAY,EACZ,IAAY,EACZ,KAAc,EACN,UAA+B,EAC/B,aAA8B,EACtC,mBAAoC;QAVtC,iBA6FC;QApFS,8BAAA,EAAA,qBAA8B;QACtC,oCAAA,EAAA,2BAAoC;QAF5B,eAAU,GAAV,UAAU,CAAqB;QAC/B,kBAAa,GAAb,aAAa,CAAiB;QAzBhC,SAAI,GAAW,CAAC,CAAC;QACjB,cAAS,GAAW,CAAC,CAAC;QA2B5B,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;YAC7D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,sBAAsB,EAAE,IAAI,KAAK,CAAC;SAC1D;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,YAAY,EAAE;gBACjB,IAAI;oBACF,YAAY,GAAG,OAAO,CAAC,8BAA8B,CAAC,CAAC;iBACxD;gBAAC,OAAO,UAAU,EAAE;oBACnB,IAAI;wBACF,YAAY,GAAG,OAAO,CAAC,4BAA4B,CAAC,CAAC;qBACtD;oBAAC,OAAO,UAAU,EAAE;wBACnB,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;wBACxC,qFAAqF;wBACrF,MAAM,UAAU,CAAC;qBAClB;iBACF;aACF;SACF;aAAM;YACL,IAAI,CAAC,YAAY,EAAE;gBACjB,IAAI;oBACF,YAAY,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;iBACrD;gBAAC,OAAO,UAAU,EAAE;oBACnB,IAAI;wBACF,YAAY,GAAG,OAAO,CAAC,yBAAyB,CAAC,CAAC;qBACnD;oBAAC,OAAO,UAAU,EAAE;wBACnB,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;wBACxC,qFAAqF;wBACrF,MAAM,UAAU,CAAC;qBAClB;iBACF;aACF;SACF;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC;QAEhE,2BAA2B;QAC3B,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAExB,uBAAuB;QACvB,IAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAElD,oBAAoB;QACpB,IAAI,IAAqC,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,GAAI,IAAI,CAAC,UAA4B,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,iBAAiB,EAAE,EAAE,mBAAmB,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACpJ;aAAM;YACL,IAAI,GAAI,IAAI,CAAC,UAA4B,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACvG,IAAI,CAAC,IAAI,GAAI,IAAuB,CAAC,GAAG,CAAC;YACzC,IAAI,CAAC,SAAS,GAAI,IAAuB,CAAC,QAAQ,CAAC;SACpD;QAED,4BAA4B;QAC5B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAEnB,0EAA0E;QAC1E,oBAAoB;QACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QAErB,uEAAuE;QACvE,IAAI,CAAC,UAAU,GAAG,IAAI,YAAM,EAAE,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,2EAA2E;QAC3E,IAAI,CAAC,mBAAmB,GAAG,IAAI,0CAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QACjF,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC;YAC/B,KAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,KAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,EAAE;YAC5B,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAM,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAChD,IAAI,CAAC,SAAS,GAAG,IAAI,YAAM,CAAC;YAC1B,EAAE,EAAE,UAAU;YACd,QAAQ,EAAE,KAAK;YACf,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAM,OAAO,GAAI,IAAI,CAAC,UAA4B,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,UAAA,CAAC,IAAI,OAAA,KAAI,CAAC,eAAe,CAAC,CAAC,CAAC,EAAvB,CAAuB,CAAC,CAAC;YAC/I,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;SAC9B;IACH,CAAC;IAnGD,sBAAW,qCAAQ;aAAnB,cAAgC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAW,sCAAS;aAApB,cAAiC,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;;;OAAA;IAC1D,sBAAW,+BAAE;aAAb,cAAuB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;OAAA;IACzC,sBAAW,qCAAQ;aAAnB,cAAgC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;OAAA;IACxD,sBAAW,gCAAG;aAAd,cAA2B,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;OAAA;IAiGvC,gCAAM,GAAb,UAAc,IAAY,EAAE,IAAY;QACtC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;gBAChC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;aAChE;YACA,IAAI,CAAC,UAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrF,OAAO;SACR;QACA,IAAI,CAAC,UAA4B,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IACnE,CAAC;IAEM,+BAAK,GAAZ;QACE,IAAI,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAA4B,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SACzE;IACH,CAAC;IAEM,8BAAI,GAAX;QAAA,iBA0CC;QAzCC,uEAAuE;QACvE,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACvB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;gBAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;gBACjC,IAAI,CAAC,sBAAsB,EAAE,CAAC,IAAI,CAAC,UAAA,kBAAkB;oBACnD,kBAAkB,CAAC,OAAO,CAAC,UAAC,GAAW;wBACrC,IAAI;4BACF,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;wBAAC,OAAO,CAAC,EAAE;4BACV,uDAAuD;yBACxD;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACF,IAAI,CAAC,UAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvE,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;aACpC;iBAAM;gBACL,6DAA6D;gBAC7D,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACxB,IAAI,CAAC,UAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;gBACvE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE;oBACzB,KAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;gBACrC,CAAC,CAAC,CAAC;aACJ;SACF;aAAM;YACL,6EAA6E;YAC7E,+DAA+D;YAC/D,2EAA2E;YAC3E,yEAAyE;YACzE,kDAAkD;YAClD,2BAA2B;YAC3B,IAAM,WAAW,GAAc,IAAI,CAAC,UAA4B,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1F,IAAI,CAAC,UAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACnE,WAAW,CAAC,OAAO,CAAC,UAAA,GAAG;gBACrB,IAAI;oBACF,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACnB;gBAAC,OAAO,CAAC,EAAE;oBACV,uDAAuD;iBACxD;YACH,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAEO,gDAAsB,GAA9B;QAAA,iBAaC;QAZC,OAAO,IAAI,OAAO,CAAW,UAAA,OAAO;YAClC,IAAM,KAAK,GAAG,oBAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,2BAA2B,CAAC,EAAE,CAAE,KAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAE,CAAC,CAAC;YACrG,KAAK,CAAC,EAAE,CAAC,SAAS,EAAE,UAAA,OAAO;gBACzB,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC;YACH,IAAM,OAAO,GAAG,UAAU,CAAC;gBACzB,qDAAqD;gBACrD,KAAK,CAAC,IAAI,EAAE,CAAC;gBACb,OAAO,CAAC,CAAE,KAAI,CAAC,SAAS,CAAE,CAAC,CAAC;YAC9B,CAAC,EAAE,IAAI,CAAC,CAAC;QACX,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sBAAW,qCAAQ;aAAnB;YACE,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,OAAO,IAAI,CAAC,SAAS,CAAC;aACvB;YACD,IAAM,cAAc,GAAI,IAAI,CAAC,UAA4B,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACtF,OAAO,cAAc,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC;QAC5D,CAAC;;;OAAA;IAEO,gDAAsB,GAA9B;QACE,IAAM,SAAS,GAAG,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,IAAI,WAAW,GAAW,CAAC,CAAC;QAC5B,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACtC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAEO,2CAAiB,GAAzB;QACE,OAAO,YAAU,IAAI,CAAC,MAAM,EAAE,GAAG,QAAU,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,yCAAe,GAAvB,UAAwB,QAAgB;QAAxC,iBAMC;QALC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,KAAI,CAAC,oBAAoB,EAAE,EAA3B,CAA2B,CAAC,CAAC;SAC/D;IACH,CAAC;IAEO,8CAAoB,GAA5B;QAAA,iBAQC;QAPC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;SACR;QACD,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,eAAe,EAAE,EAAtB,CAAsB,EAAE,mBAAmB,CAAC,CAAC;IACrF,CAAC;IAEO,yCAAe,GAAvB;QACE,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAC5B,CAAC;IACH,sBAAC;AAAD,CAAC,AAhPD,IAgPC;AAhPY,0CAAe;AAkP5B,gFAAgF;AAChF,8EAA8E;AAC9E,kBAAkB;AAClB,SAAgB,iBAAiB,CAAC,IAAY,EAAE,IAAuB;IACrE,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QACvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC;SACb;QACD,OAAU,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC,SAAI,IAAM,CAAC;KACjD;IACD,IAAM,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IACpB,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,EAAE;QACzD,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,MAAM,IAAI,GAAG,CAAC;SACf;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,qEAAqE;QACrE,IAAM,yBAAyB,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACvF,IAAM,oBAAoB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjF,IAAM,KAAK,GACT,GAAG,KAAK,EAAE;YACV,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBACxB,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACzB,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;oBACjB,CAAC,yBAAyB,IAAI,oBAAoB,CAAC,CAAC,CAAC;QACvD,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,IAAI,CAAC;SAChB;QACD,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YACjB,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,EAAE,CAAC;aACX;iBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;gBACpB,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5C,MAAM,IAAI,GAAG,CAAC;gBACd,OAAO,GAAG,CAAC,CAAC;aACb;iBAAM;gBACL,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACpC,OAAO,GAAG,CAAC,CAAC;gBACZ,MAAM,IAAI,CAAC,CAAC;aACb;SACF;QACD,IAAI,KAAK,EAAE;YACT,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;YACxC,MAAM,IAAI,IAAI,CAAC;SAChB;aAAM;YACL,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACrC;KACF;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAlDD,8CAkDC;AAED,SAAS,aAAa,CAAC,IAAuB;IAC5C,OAAO,OAAO,IAAI,KAAK,QAAQ,CAAC;AAClC,CAAC;AAED,SAAS,UAAU,CAAC,IAAY,EAAE,KAAa;IAC7C,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QAC9B,MAAM,IAAI,IAAI,CAAC;KAChB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,GAAG,CAAC,IAAa,EAAE,IAAa;IACvC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;AAC9C,CAAC"}