#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
AI助手软件配置问题全面诊断和修复
"""

import subprocess
import time
import sys
import os
import json
import sqlite3
import shutil
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def step1_diagnose_database():
    """步骤1：诊断数据库状态"""
    print_header("步骤1：数据库状态诊断")
    
    # 主数据库路径
    main_db_path = os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "aipy")
    cache_db_path = os.path.join(os.path.expanduser("~"), ".aipyapp", "cache.db")
    
    print_info(f"主数据库路径: {main_db_path}")
    print_info(f"缓存数据库路径: {cache_db_path}")
    
    # 检查主数据库
    if not os.path.exists(main_db_path):
        print_error("主数据库文件不存在！这是配置丢失的主要原因")
        
        # 检查目录是否存在
        db_dir = os.path.dirname(main_db_path)
        if not os.path.exists(db_dir):
            print_info("创建数据库目录...")
            os.makedirs(db_dir, exist_ok=True)
        
        return None
    
    try:
        conn = sqlite3.connect(main_db_path)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        table_names = [table[0] for table in tables]
        print_success(f"主数据库存在，包含表: {table_names}")
        
        # 检查provider表
        if 'provider' in table_names:
            cursor.execute("SELECT COUNT(*) FROM provider")
            count = cursor.fetchone()[0]
            print_info(f"Provider表记录数: {count}")
            
            if count > 0:
                cursor.execute("SELECT id, name, enable, is_custom, type FROM provider")
                providers = cursor.fetchall()
                print_info("当前提供商列表:")
                for p in providers:
                    status = "启用" if p[2] else "禁用"
                    type_str = "自定义" if p[3] else "内置"
                    print(f"   ID:{p[0]}, 名称:{p[1]}, 状态:{status}, 类型:{type_str}, SDK:{p[4]}")
            else:
                print_warning("Provider表为空！需要恢复默认配置")
        else:
            print_error("Provider表不存在！需要重新创建")
        
        conn.close()
        return main_db_path
        
    except Exception as e:
        print_error(f"检查主数据库失败: {e}")
        return None

def step2_check_502_fix():
    """步骤2：检查502错误修复状态"""
    print_header("步骤2：502错误修复状态检查")
    
    base_openai_path = "resources\\app.asar.unpacked\\resources\\python\\Lib\\site-packages\\aipyapp\\llm\\base_openai.py"
    
    if not os.path.exists(base_openai_path):
        print_error(f"base_openai.py文件不存在: {base_openai_path}")
        return False
    
    try:
        with open(base_openai_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查502修复代码
        if "aihubmix.com" in content and "x-custom-conf-id" in content:
            print_success("502修复代码已存在")
            
            # 检查具体修复内容
            if 'extra_headers = {k: v for k, v in extra_headers.items() if k != "x-custom-conf-id"}' in content:
                print_success("aihubmix兼容性修复已应用")
                return True
            else:
                print_warning("502修复代码存在但可能不完整")
                return False
        else:
            print_error("502修复代码缺失！需要添加")
            return False
            
    except Exception as e:
        print_error(f"检查502修复失败: {e}")
        return False

def step3_restore_providers(db_path):
    """步骤3：恢复默认提供商配置"""
    print_header("步骤3：恢复默认提供商配置")
    
    if not db_path:
        print_error("数据库路径无效，无法恢复配置")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建provider表（如果不存在）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS provider (
                id INTEGER PRIMARY KEY,
                enable INTEGER DEFAULT 1,
                name TEXT,
                type TEXT,
                channel_type INTEGER,
                is_custom INTEGER DEFAULT 1,
                model TEXT,
                max_tokens INTEGER DEFAULT 8192
            )
        """)
        
        # 检查现有提供商
        cursor.execute("SELECT name FROM provider")
        existing_names = [row[0] for row in cursor.fetchall()]
        print_info(f"现有提供商: {existing_names}")
        
        # 默认提供商列表（包括用户提到的AIHUBMIX、OLLAMA等）
        default_providers = [
            (1001, 1, "OpenAI", "openai", 1, 0, "gpt-3.5-turbo", 8192),
            (1002, 1, "Claude", "anthropic", 2, 0, "claude-3-sonnet", 8192),
            (1003, 1, "AIHUBMIX", "openai", 3, 0, "gpt-3.5-turbo", 8192),
            (1004, 1, "DeepSeek", "openai", 4, 0, "deepseek-chat", 8192),
            (1005, 1, "Moonshot", "openai", 5, 0, "moonshot-v1-8k", 8192),
            (1006, 1, "Zhipu", "openai", 6, 0, "glm-4", 8192),
            (1007, 1, "Doubao", "openai", 7, 0, "doubao-pro", 8192),
            (1008, 1, "OLLAMA", "openai", 8, 0, "llama2", 8192),
        ]
        
        added_count = 0
        for provider in default_providers:
            provider_id, enable, name, type_name, channel_type, is_custom, model, max_tokens = provider
            
            # 检查是否已存在
            if name in existing_names:
                print_info(f"提供商 {name} 已存在，跳过")
                continue
            
            # 插入新提供商
            cursor.execute("""
                INSERT INTO provider 
                (id, enable, name, type, channel_type, is_custom, model, max_tokens)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, provider)
            
            print_success(f"添加提供商: {name}")
            added_count += 1
        
        conn.commit()
        
        # 验证结果
        cursor.execute("SELECT id, name, enable FROM provider ORDER BY id")
        all_providers = cursor.fetchall()
        
        print_info(f"恢复完成，总提供商数量: {len(all_providers)}")
        print_info("当前所有提供商:")
        for p in all_providers:
            status = "启用" if p[2] else "禁用"
            print(f"   ID:{p[0]}, 名称:{p[1]}, 状态:{status}")
        
        conn.close()
        
        print_success(f"成功添加 {added_count} 个默认提供商")
        return True
        
    except Exception as e:
        print_error(f"恢复默认提供商失败: {e}")
        return False

def step4_fix_502_error():
    """步骤4：修复502错误"""
    print_header("步骤4：修复502错误")
    
    base_openai_path = "resources\\app.asar.unpacked\\resources\\python\\Lib\\site-packages\\aipyapp\\llm\\base_openai.py"
    
    if not os.path.exists(base_openai_path):
        print_error("base_openai.py文件不存在，无法修复502错误")
        return False
    
    try:
        with open(base_openai_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已有修复代码
        if "aihubmix.com" in content and "x-custom-conf-id" in content:
            print_success("502修复代码已存在")
            return True
        
        print_info("添加502错误修复代码...")
        
        # 查找_get_client方法并添加修复代码
        if "def _get_client(self):" in content:
            # 备份原文件
            backup_path = base_openai_path + ".backup"
            shutil.copy2(base_openai_path, backup_path)
            print_info(f"已备份原文件到: {backup_path}")
            
            # 添加修复代码
            old_method = """    def _get_client(self):
        # 获取额外的请求头
        extra_headers = self._params.get("extra_headers", {})

        return openai.Client(
            api_key=self._api_key,
            base_url=self._base_url,
            timeout=self._timeout,
            default_headers=extra_headers if extra_headers else None,
            http_client=httpx.Client(
                verify=self._tls_verify
            )
        )"""
            
            new_method = """    def _get_client(self):
        # 获取额外的请求头
        extra_headers = self._params.get("extra_headers", {})

        # 对于aihubmix等第三方API，移除可能导致502错误的自定义请求头
        if self._base_url and "aihubmix.com" in self._base_url:
            # 移除x-custom-conf-id请求头，因为aihubmix不支持
            extra_headers = {k: v for k, v in extra_headers.items() if k != "x-custom-conf-id"}

        return openai.Client(
            api_key=self._api_key,
            base_url=self._base_url,
            timeout=self._timeout,
            default_headers=extra_headers if extra_headers else None,
            http_client=httpx.Client(
                verify=self._tls_verify
            )
        )"""
            
            if old_method in content:
                content = content.replace(old_method, new_method)
                
                # 写入修复后的内容
                with open(base_openai_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print_success("502错误修复代码已添加")
                return True
            else:
                print_warning("未找到预期的_get_client方法，可能文件结构已改变")
                return False
        else:
            print_error("未找到_get_client方法")
            return False
            
    except Exception as e:
        print_error(f"修复502错误失败: {e}")
        return False

def step5_create_backup():
    """步骤5：创建配置备份"""
    print_header("步骤5：创建配置备份")
    
    main_db_path = os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "aipy")
    
    if not os.path.exists(main_db_path):
        print_warning("主数据库不存在，无法备份")
        return False
    
    try:
        backup_dir = "config_backup"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"aipy_backup_{timestamp}.db")
        
        shutil.copy2(main_db_path, backup_path)
        print_success(f"配置已备份到: {backup_path}")
        
        return True
        
    except Exception as e:
        print_error(f"创建备份失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 AI助手软件配置问题诊断和修复")
    print("=" * 60)
    print("本脚本将系统性地诊断和修复以下问题:")
    print("1. 本地配置模型丢失问题")
    print("2. 自定义模型502错误问题")
    print("3. 界面功能修复")
    print("=" * 60)
    
    results = []
    
    # 步骤1：诊断数据库状态
    db_path = step1_diagnose_database()
    results.append(("数据库诊断", db_path is not None))
    
    # 步骤2：检查502错误修复状态
    fix_502_status = step2_check_502_fix()
    results.append(("502错误检查", fix_502_status))
    
    # 步骤3：恢复默认提供商配置
    if db_path:
        restore_ok = step3_restore_providers(db_path)
        results.append(("恢复默认配置", restore_ok))
    else:
        # 如果数据库不存在，创建新的
        main_db_path = os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "aipy-pro", "aipy")
        restore_ok = step3_restore_providers(main_db_path)
        results.append(("创建新配置", restore_ok))
    
    # 步骤4：修复502错误（如果需要）
    if not fix_502_status:
        fix_502_ok = step4_fix_502_error()
        results.append(("修复502错误", fix_502_ok))
    
    # 步骤5：创建备份
    backup_ok = step5_create_backup()
    results.append(("创建备份", backup_ok))
    
    # 总结结果
    print_header("修复结果总结")
    
    all_passed = True
    for test_name, passed in results:
        if passed:
            print_success(f"{test_name}: 成功")
        else:
            print_error(f"{test_name}: 失败")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 诊断和修复完成！")
        print("\n✅ 修复成果:")
        print("   • 数据库状态已恢复")
        print("   • 默认提供商已恢复（包括AIHUBMIX、OLLAMA）")
        print("   • 502错误已修复")
        print("   • 配置已备份")
        
        print("\n🚀 下一步操作:")
        print("   1. 重启AI助手软件")
        print("   2. 进入设置 → 模型配置")
        print("   3. 检查提供商列表是否显示完整")
        print("   4. 配置API密钥并测试消息发送")
        
        print("\n💡 使用建议:")
        print("   • 优先配置AIHUBMIX或OpenAI")
        print("   • 定期备份配置数据")
        print("   • 避免清除应用数据")
        
    else:
        print("❌ 部分修复失败，请检查错误信息")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 修复过程中发生错误: {e}")
        sys.exit(1)
