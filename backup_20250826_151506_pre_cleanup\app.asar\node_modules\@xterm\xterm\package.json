{"name": "@xterm/xterm", "description": "Full xterm terminal, in your browser", "version": "5.5.0", "main": "lib/xterm.js", "style": "css/xterm.css", "types": "typings/xterm.d.ts", "repository": "https://github.com/xtermjs/xterm.js", "license": "MIT", "devDependencies": {"@lunapaint/png-codec": "^0.2.0", "@playwright/test": "^1.37.1", "@types/chai": "^4.2.22", "@types/debug": "^4.1.7", "@types/deep-equal": "^1.0.1", "@types/express": "4", "@types/express-ws": "^3.0.1", "@types/glob": "^7.2.0", "@types/jsdom": "^16.2.13", "@types/mocha": "^9.0.0", "@types/node": "^18.16.0", "@types/utf8": "^3.0.0", "@types/webpack": "^5.28.0", "@types/ws": "^8.2.0", "@typescript-eslint/eslint-plugin": "^6.2.00", "@typescript-eslint/parser": "^6.2.00", "chai": "^4.3.4", "cross-env": "^7.0.3", "deep-equal": "^2.0.5", "eslint": "^8.56.0", "eslint-plugin-jsdoc": "^46.9.1", "express": "^4.17.1", "express-ws": "^5.0.2", "glob": "^7.2.0", "jsdom": "^18.0.1", "mocha": "^10.1.0", "mustache": "^4.2.0", "node-pty": "1.1.0-beta5", "nyc": "^15.1.0", "source-map-loader": "^3.0.0", "source-map-support": "^0.5.20", "ts-loader": "^9.3.1", "typescript": "^5.1.6", "utf8": "^3.0.0", "webpack": "^5.61.0", "webpack-cli": "^4.9.1", "ws": "^8.2.3", "xterm-benchmark": "^0.3.1"}}