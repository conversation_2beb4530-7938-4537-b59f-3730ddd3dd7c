#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试502错误问题 - 对比本地配置和自定义配置的差异
"""

import subprocess
import time
import sys
import os
import json
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def analyze_provider_differences():
    """分析本地配置和自定义配置的差异"""
    print_header("分析提供商配置差异")
    
    print_info("根据代码分析，发现以下关键差异:")
    print()
    
    print("📋 本地TOML配置模型:")
    print("   • provider_id: -1 (负数)")
    print("   • channelType: -1")
    print("   • 不会添加 x-custom-conf-id 请求头")
    print("   • ✅ 正常工作")
    print()
    
    print("📋 自定义模型配置:")
    print("   • provider_id: > 0 (正数)")
    print("   • channelType: > 0")
    print("   • 会尝试添加 x-custom-conf-id 请求头")
    print("   • ❌ 可能导致502错误")
    print()
    
    print("🔍 关键代码位置:")
    print("   • extracted_app/out/main/index.js 第261行")
    print("   • resources\\app.asar.unpacked\\resources\\python\\aipy.py 第85-96行")
    print("   • resources\\app.asar.unpacked\\resources\\aipy-stdio\\aipy.py 第78-89行")

def check_debug_modifications():
    """检查调试修改是否已应用"""
    print_header("检查调试修改状态")
    
    files_to_check = [
        "resources\\app.asar.unpacked\\resources\\python\\aipy.py",
        "resources\\app.asar.unpacked\\resources\\aipy-stdio\\aipy.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "[DEBUG]" in content and "aihubmix" in content:
                    print_success(f"调试代码已添加: {file_path}")
                else:
                    print_warning(f"调试代码可能缺失: {file_path}")
                    
            except Exception as e:
                print_error(f"检查文件失败 {file_path}: {e}")
        else:
            print_error(f"文件不存在: {file_path}")

def generate_test_instructions():
    """生成测试说明"""
    print_header("测试说明")
    
    instructions = """
🧪 测试步骤:

1. **重启AI助手软件**
   - 确保调试代码生效

2. **测试本地配置模型**
   - 使用TOML配置的aihubmix模型
   - 发送测试消息
   - 观察是否正常工作

3. **测试自定义配置模型**
   - 添加新的aihubmix自定义模型
   - 使用相同的API密钥和URL
   - 发送测试消息
   - 观察控制台调试输出

4. **查看调试日志**
   - 检查软件日志中的[DEBUG]输出
   - 确认provider_id和base_url的值
   - 确认是否正确跳过了x-custom-conf-id

🔍 预期结果:

**本地配置模型:**
- 不应该看到[DEBUG]输出（因为provider_id=-1）
- 应该正常工作

**自定义配置模型:**
- 应该看到[DEBUG]输出
- 应该显示"Skipped x-custom-conf-id for aihubmix"
- 应该正常工作，不再出现502错误

🚨 如果仍然出现502错误:

可能的原因:
1. base_url检测逻辑有问题
2. config.get("base_url")返回的值不包含"aihubmix.com"
3. 还有其他地方添加了不兼容的请求头

解决方案:
1. 检查调试输出中的base_url值
2. 可能需要调整检测逻辑
3. 可能需要检查其他可能添加请求头的地方
    """
    
    print(instructions)

def create_comparison_summary():
    """创建对比总结"""
    print_header("修复总结")
    
    summary = """
🎯 502错误根本原因确认:

**问题机制:**
1. 本地TOML配置: provider_id = -1 → 不添加x-custom-conf-id → 正常工作
2. 自定义配置: provider_id > 0 → 添加x-custom-conf-id → aihubmix拒绝 → 502错误

**修复策略:**
1. 在aipy.py中添加aihubmix检测逻辑
2. 对于包含"aihubmix.com"的base_url，跳过添加x-custom-conf-id
3. 添加调试输出确认修复效果

**修复文件:**
• resources\\app.asar.unpacked\\resources\\python\\aipy.py
• resources\\app.asar.unpacked\\resources\\aipy-stdio\\aipy.py
• resources\\app.asar.unpacked\\resources\\python\\Lib\\site-packages\\aipyapp\\llm\\base_openai.py

**验证方法:**
1. 重启软件
2. 测试自定义aihubmix模型
3. 检查调试日志
4. 确认不再出现502错误

如果问题仍然存在，请检查调试输出中的base_url值，
可能需要进一步调整检测逻辑。
    """
    
    print(summary)
    
    # 保存总结到文件
    try:
        with open("502错误调试总结.txt", "w", encoding="utf-8") as f:
            f.write(summary)
        print_success("调试总结已保存到: 502错误调试总结.txt")
    except Exception as e:
        print_warning(f"保存调试总结失败: {e}")

def main():
    """主函数"""
    print("🔧 AI助手软件502错误调试分析")
    print("=" * 60)
    print("本脚本分析本地配置和自定义配置的差异")
    print("=" * 60)
    
    # 分析差异
    analyze_provider_differences()
    
    # 检查修改状态
    check_debug_modifications()
    
    # 生成测试说明
    generate_test_instructions()
    
    # 创建总结
    create_comparison_summary()
    
    print("\n" + "=" * 60)
    print("🎉 调试分析完成！")
    print("请按照测试说明进行验证，并查看调试输出")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 分析过程中发生错误: {e}")
        sys.exit(1)
