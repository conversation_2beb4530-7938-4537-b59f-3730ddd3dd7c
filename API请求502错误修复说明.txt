🔧 AI助手软件 - API请求502错误修复说明

📋 问题描述:
使用相同的API密钥和aihubmix提供商时，出现不一致的行为：
• 本地配置的模型可以正常发起API请求并获得响应
• 自定义模型配置无法正常请求，返回502错误

🔍 问题根源分析:

**本地配置 vs 自定义模型配置的差异：**

1. **本地配置**：
   - 直接使用aihubmix客户端
   - 没有额外的自定义请求头
   - API请求正常工作

2. **自定义模型配置**：
   - 会被分配一个provider_id > 0
   - 系统自动添加"x-custom-conf-id"请求头
   - aihubmix API不识别此请求头，返回502错误

**关键代码位置：**
- aipy.py 第85-88行（多个文件）
- base_openai.py 第21-29行

**问题机制：**
```python
if provider_id > 0:
    extra_headers = self.current._params.get("extra_headers", {})
    extra_headers["x-custom-conf-id"] = str(provider_id)  # 这里导致502错误
    self.current._params["extra_headers"] = extra_headers
```

🛠️ 修复方案:

**1. 修复OpenAI客户端初始化**
文件：`resources\app.asar.unpacked\resources\python\Lib\site-packages\aipyapp\llm\base_openai.py`

修复内容：
- 添加extra_headers处理逻辑
- 对aihubmix API移除不支持的x-custom-conf-id请求头
- 使用default_headers参数传递有效的请求头

**2. 修复自定义请求头添加逻辑**
文件：
- `resources\app.asar.unpacked\resources\python\aipy.py`
- `extracted_app\resources\aipy-stdio\aipy.py`
- `resources\app.asar.unpacked\resources\aipy-stdio\aipy.py`

修复内容：
- 检测aihubmix API URL
- 对aihubmix跳过添加x-custom-conf-id请求头
- 保持其他API的兼容性

🎯 修复效果:

✅ **解决502错误**
- 自定义模型配置不再发送不支持的请求头
- aihubmix API请求正常工作

✅ **保持兼容性**
- 其他API提供商仍然接收x-custom-conf-id请求头
- 本地配置继续正常工作

✅ **统一行为**
- 本地配置和自定义模型配置行为一致
- 相同API密钥产生相同结果

🔧 技术细节:

**修复前的问题：**
```
自定义模型 → provider_id > 0 → 添加x-custom-conf-id → aihubmix拒绝 → 502错误
本地配置 → provider_id = 0 → 无额外请求头 → aihubmix接受 → 正常响应
```

**修复后的逻辑：**
```
自定义模型 → 检测aihubmix → 跳过x-custom-conf-id → aihubmix接受 → 正常响应
本地配置 → 无变化 → 继续正常工作
其他API → 继续接收x-custom-conf-id → 保持兼容性
```

🚀 验证方法:

1. **重启AI助手软件**
2. **测试自定义模型配置**：
   - 添加aihubmix自定义模型
   - 使用相同的API密钥
   - 发起API请求
   - 应该不再出现502错误

3. **测试本地配置**：
   - 确保本地配置继续正常工作
   - 验证API请求正常

4. **测试其他提供商**：
   - 验证OpenAI、Claude等其他提供商
   - 确保没有破坏现有功能

📝 支持的提供商:

**已优化的提供商：**
- aihubmix：移除x-custom-conf-id请求头

**保持原有逻辑的提供商：**
- OpenAI：继续使用x-custom-conf-id
- Claude：继续使用x-custom-conf-id
- DeepSeek：继续使用x-custom-conf-id
- 其他自定义提供商：继续使用x-custom-conf-id

⚠️ 注意事项:

1. **API密钥安全**：确保API密钥的安全性
2. **网络连接**：确保网络连接正常
3. **API配额**：注意API使用配额限制
4. **错误处理**：如遇其他错误，检查API密钥和网络

🔄 回滚方案:

如果修复导致其他问题，可以通过以下方式回滚：
1. 恢复原始的base_openai.py文件
2. 恢复原始的aipy.py文件
3. 重启软件

📞 技术支持:

如果问题仍然存在，请检查：
1. API密钥是否有效
2. 网络连接是否正常
3. aihubmix服务是否可用
4. 软件是否已重启
