#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复自定义模型配置问题
"""

import subprocess
import time
import sys
import os
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🔍 {title}")
    print("=" * 60)

def print_success(message):
    """打印成功消息"""
    print(f"✅ {message}")

def print_warning(message):
    """打印警告消息"""
    print(f"⚠️ {message}")

def print_error(message):
    """打印错误消息"""
    print(f"❌ {message}")

def print_info(message):
    """打印信息消息"""
    print(f"ℹ️ {message}")

def fix_api_key_transmission():
    """修复API密钥传递问题"""
    print_header("修复API密钥传递问题")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找taskUpdateProviderById方法
        old_method = 'n={...n,max_tokens:r.maxTokens||1024,base_url:r.baseUrl||"",baseUrl:r.baseUrl||"",api_key:r.apiKey||""}'
        new_method = 'n={...n,max_tokens:r.maxTokens||1024,base_url:r.baseUrl||"",baseUrl:r.baseUrl||"",api_key:r.apiKey||"",api_base:r.baseUrl||""}'
        
        if old_method in content:
            content = content.replace(old_method, new_method)
            print_success("已修复API密钥传递逻辑")
        else:
            print_warning("未找到预期的API密钥传递代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复API密钥传递失败: {e}")
        return False

def fix_provider_type_preservation():
    """修复提供商类型保持问题"""
    print_header("修复提供商类型保持问题")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复providerCreateOne中的类型设置
        old_insert = 'this.#e.stmt.providerInsertOne.run(newId, 1, e, supplier.sdk, t, 1, i || "auto", r || 8192);'
        new_insert = '''// 保持用户选择的提供商类型，不使用supplier.sdk
  const providerType = e.toLowerCase().includes("aihubmix") ? "aihubmix" : 
                      e.toLowerCase().includes("claude") ? "anthropic" :
                      e.toLowerCase().includes("deepseek") ? "deepseek" :
                      e.toLowerCase().includes("moonshot") ? "moonshot" :
                      e.toLowerCase().includes("zhipu") ? "zhipu" :
                      e.toLowerCase().includes("doubao") ? "doubao" :
                      e.toLowerCase().includes("ollama") ? "ollama" :
                      supplier.sdk || "openai";
  
  this.#e.stmt.providerInsertOne.run(newId, 1, e, providerType, t, 1, i || "auto", r || 8192);'''
        
        if old_insert in content:
            content = content.replace(old_insert, new_insert)
            print_success("已修复提供商类型保持逻辑")
        else:
            print_warning("未找到预期的提供商插入代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复提供商类型保持失败: {e}")
        return False

def fix_provider_deletion():
    """修复提供商删除问题"""
    print_header("修复提供商删除问题")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找providerDeleteById方法
        old_delete = '''providerDeleteById=async e=>{
// 离线模式：只删除本地数据库记录
console.log("[OFFLINE] Deleting provider locally:", e);
try {
  this.#e.stmt.providerDeleteById.run(e);
  console.log("[OFFLINE] Provider deleted successfully");
} catch(error) {
  console.error("[OFFLINE] Error deleting provider:", error);
  throw error;
}'''
        
        new_delete = '''providerDeleteById=async e=>{
// 离线模式：删除本地数据库记录，支持按名称或ID删除
console.log("[OFFLINE] Deleting provider locally:", e);
try {
  // 尝试按ID删除
  let result = this.#e.stmt.providerDeleteById.run(e);
  
  // 如果按ID删除失败，尝试按名称删除
  if (result.changes === 0) {
    result = this.#e.prepare("DELETE FROM provider WHERE name = ? AND is_custom = 1").run(e);
  }
  
  if (result.changes > 0) {
    console.log("[OFFLINE] Provider deleted successfully, changes:", result.changes);
  } else {
    console.warn("[OFFLINE] No provider found to delete:", e);
    throw new Error(`Provider ${e} not found`);
  }
} catch(error) {
  console.error("[OFFLINE] Error deleting provider:", error);
  throw error;
}'''
        
        if old_delete in content:
            content = content.replace(old_delete, new_delete)
            print_success("已修复提供商删除逻辑")
        else:
            print_warning("未找到预期的提供商删除代码")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"修复提供商删除失败: {e}")
        return False

def add_debug_logging():
    """添加调试日志"""
    print_header("添加调试日志")
    
    js_file = "extracted_app/out/main/index.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 在taskUpdateProviderById中添加调试日志
        old_provider_change = 'await this.#t[e].providerChange(n)'
        new_provider_change = '''console.log("[DEBUG] Provider config being sent:", JSON.stringify(n, null, 2));
      await this.#t[e].providerChange(n)'''
        
        if old_provider_change in content:
            content = content.replace(old_provider_change, new_provider_change)
            print_success("已添加提供商配置调试日志")
        
        # 保存修改
        with open(js_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print_error(f"添加调试日志失败: {e}")
        return False

def create_test_script():
    """创建测试脚本"""
    print_header("创建测试脚本")
    
    test_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自定义模型配置测试脚本
"""

import subprocess
import time
import sys
import os

def test_custom_provider():
    """测试自定义提供商功能"""
    print("🧪 自定义模型配置测试")
    print("=" * 50)
    
    print("请按以下步骤测试:")
    print()
    print("1. **重启AI助手软件**")
    print("   - 确保所有修改生效")
    print()
    print("2. **添加自定义aihubmix模型**:")
    print("   - 进入设置 → 模型配置")
    print("   - 点击'添加'按钮")
    print("   - 名称: 输入 'My AIHUBMIX'")
    print("   - 提供商: 选择 'aihubmix'")
    print("   - API地址: https://api.aihubmix.com/v1")
    print("   - API密钥: 输入您的真实密钥")
    print("   - 保存配置")
    print()
    print("3. **验证配置**:")
    print("   - 检查提供商类型是否保持为'aihubmix'")
    print("   - 检查是否能正常选择该模型")
    print()
    print("4. **测试消息发送**:")
    print("   - 选择刚添加的自定义模型")
    print("   - 发送测试消息: '今天几月几号'")
    print("   - 观察是否还出现401或502错误")
    print()
    print("5. **测试删除功能**:")
    print("   - 尝试删除刚添加的自定义模型")
    print("   - 检查是否能正常删除")
    print()
    print("🔍 预期结果:")
    print("✅ 提供商类型保持为'aihubmix'")
    print("✅ 不再出现401认证错误")
    print("✅ 不再出现502网关错误")
    print("✅ 能正常发送和接收消息")
    print("✅ 能正常删除自定义模型")
    print()
    print("如果仍有问题，请查看软件日志中的[DEBUG]输出")

if __name__ == "__main__":
    test_custom_provider()
'''
    
    try:
        with open("test_custom_provider.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        print_success("测试脚本已创建: test_custom_provider.py")
        return True
    except Exception as e:
        print_error(f"创建测试脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 自定义模型配置问题修复")
    print("=" * 60)
    print("本脚本将修复以下问题:")
    print("1. 401认证错误 - API密钥传递问题")
    print("2. 提供商类型自动变更问题")
    print("3. 自定义模型无法删除问题")
    print("=" * 60)
    
    results = []
    
    # 修复API密钥传递
    api_key_ok = fix_api_key_transmission()
    results.append(("API密钥传递修复", api_key_ok))
    
    # 修复提供商类型保持
    type_ok = fix_provider_type_preservation()
    results.append(("提供商类型保持修复", type_ok))
    
    # 修复提供商删除
    delete_ok = fix_provider_deletion()
    results.append(("提供商删除修复", delete_ok))
    
    # 添加调试日志
    debug_ok = add_debug_logging()
    results.append(("调试日志添加", debug_ok))
    
    # 创建测试脚本
    test_ok = create_test_script()
    results.append(("测试脚本创建", test_ok))
    
    # 总结结果
    print_header("修复结果总结")
    
    all_passed = True
    for test_name, passed in results:
        if passed:
            print_success(f"{test_name}: 成功")
        else:
            print_error(f"{test_name}: 失败")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有修复完成！")
        print("\n🚀 下一步:")
        print("   1. 重新打包应用: asar pack extracted_app resources\\app.asar")
        print("   2. 重启AI助手软件")
        print("   3. 运行测试: python test_custom_provider.py")
        print("   4. 按照测试说明验证修复效果")
        
    else:
        print("❌ 部分修复失败，请检查错误信息")
    
    print("=" * 60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 修复被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 修复过程中发生错误: {e}")
        sys.exit(1)
