@echo off
chcp 65001 >nul
echo 🔧 强制修复aihubmix API地址脚本
echo =====================================

echo.
echo 📋 正在强制修复aihubmix API地址问题...
echo.

REM 1. 强制关闭所有相关进程
echo ⏹️  正在强制关闭所有相关进程...
taskkill /f /im "AI助手.exe" 2>nul
taskkill /f /im "aipyapp.exe" 2>nul
taskkill /f /im "electron.exe" 2>nul
taskkill /f /im "python.exe" 2>nul
taskkill /f /im "pythonw.exe" 2>nul
timeout /t 5 /nobreak >nul

REM 2. 删除所有可能的数据库文件
echo 🗄️  正在删除所有数据库文件...
if exist "%USERPROFILE%\.aipyapp\*.db" (
    echo    - 删除: %USERPROFILE%\.aipyapp\*.db
    del /f /q "%USERPROFILE%\.aipyapp\*.db" 2>nul
)

if exist "%APPDATA%\aipyapp\*.db" (
    echo    - 删除: %APPDATA%\aipyapp\*.db
    del /f /q "%APPDATA%\aipyapp\*.db" 2>nul
)

if exist "%LOCALAPPDATA%\aipyapp\*.db" (
    echo    - 删除: %LOCALAPPDATA%\aipyapp\*.db
    del /f /q "%LOCALAPPDATA%\aipyapp\*.db" 2>nul
)

REM 3. 删除所有缓存文件
echo 🧹 正在删除所有缓存文件...
if exist "%USERPROFILE%\.aipyapp\cache*" (
    echo    - 删除: %USERPROFILE%\.aipyapp\cache*
    del /f /q "%USERPROFILE%\.aipyapp\cache*" 2>nul
)

if exist "%APPDATA%\aipyapp\cache*" (
    echo    - 删除: %APPDATA%\aipyapp\cache*
    del /f /q "%APPDATA%\aipyapp\cache*" 2>nul
)

if exist "%LOCALAPPDATA%\aipyapp\cache*" (
    echo    - 删除: %LOCALAPPDATA%\aipyapp\cache*
    del /f /q "%LOCALAPPDATA%\aipyapp\cache*" 2>nul
)

REM 4. 删除Electron应用缓存
echo ⚡ 正在删除Electron应用缓存...
if exist "%APPDATA%\AI助手" (
    echo    - 删除: %APPDATA%\AI助手
    rmdir /s /q "%APPDATA%\AI助手" 2>nul
)

if exist "%LOCALAPPDATA%\AI助手" (
    echo    - 删除: %LOCALAPPDATA%\AI助手
    rmdir /s /q "%LOCALAPPDATA%\AI助手" 2>nul
)

REM 5. 删除浏览器缓存
echo 🌐 正在删除浏览器缓存...
if exist "%LOCALAPPDATA%\AI助手\User Data" (
    echo    - 删除: %LOCALAPPDATA%\AI助手\User Data
    rmdir /s /q "%LOCALAPPDATA%\AI助手\User Data" 2>nul
)

REM 6. 删除配置文件
echo 📁 正在删除配置文件...
if exist "%USERPROFILE%\.aipyapp\*.toml" (
    echo    - 删除: %USERPROFILE%\.aipyapp\*.toml
    del /f /q "%USERPROFILE%\.aipyapp\*.toml" 2>nul
)

if exist "%USERPROFILE%\.aipyapp\*.json" (
    echo    - 删除: %USERPROFILE%\.aipyapp\*.json
    del /f /q "%USERPROFILE%\.aipyapp\*.json" 2>nul
)

REM 7. 删除工作目录
echo 📂 正在删除工作目录...
if exist "work" (
    echo    - 删除: work目录
    rmdir /s /q "work" 2>nul
)

REM 8. 删除临时文件
echo 🗑️  正在删除临时文件...
if exist "%TEMP%\aipyapp*" (
    echo    - 删除: %TEMP%\aipyapp*
    rmdir /s /q "%TEMP%\aipyapp*" 2>nul
)

if exist "%TEMP%\AI助手*" (
    echo    - 删除: %TEMP%\AI助手*
    rmdir /s /q "%TEMP%\AI助手*" 2>nul
)

REM 9. 清除注册表缓存（可选）
echo 🔧 正在清除注册表缓存...
reg delete "HKCU\Software\aipyapp" /f 2>nul
reg delete "HKCU\Software\AI助手" /f 2>nul

echo.
echo ✅ 强制清除完成！
echo.
echo 📝 已清除的内容：
echo    ✓ 所有数据库文件 (*.db)
echo    ✓ 所有缓存文件 (cache*)
echo    ✓ Electron应用缓存
echo    ✓ 浏览器缓存
echo    ✓ 配置文件 (*.toml, *.json)
echo    ✓ 工作目录
echo    ✓ 临时文件
echo    ✓ 注册表缓存
echo.
echo 🚀 现在请重新启动软件！
echo.
echo ⚠️  重要提醒：
echo    • 重启后需要重新配置API密钥
echo    • 自定义设置将被重置
echo    • 对话历史可能丢失
echo.
echo 🎯 验证步骤：
echo    1. 重新启动AI助手软件
echo    2. 进入设置 → 模型配置
echo    3. 点击"添加"按钮
echo    4. 选择"aihubmix"提供商
echo    5. 检查API地址是否显示：https://api.aihubmix.com/v1
echo.
echo 按任意键退出...
pause >nul
