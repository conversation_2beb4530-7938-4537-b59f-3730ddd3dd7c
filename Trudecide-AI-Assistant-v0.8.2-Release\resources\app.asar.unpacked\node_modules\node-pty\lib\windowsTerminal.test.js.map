{"version": 3, "file": "windowsTerminal.test.js", "sourceRoot": "", "sources": ["../src/windowsTerminal.test.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,uBAAyB;AACzB,+BAAiC;AACjC,qDAAoD;AACpD,2BAA6B;AAC7B,gCAAkC;AAYlC,SAAS,mBAAmB,CAAC,YAA2B,EAAE,UAAwB,EAAE,SAAwB;IAAlD,2BAAA,EAAA,gBAAwB;IAAE,0BAAA,EAAA,gBAAwB;IAC1G,OAAO,IAAI,OAAO,CAAO,UAAA,OAAO;QAC9B,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAM,QAAQ,GAAG,WAAW,CAAC;YAC3B,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;gBAC3B,IAAI,OAAO,GAAG,IAAI,CAAC;gBACnB,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAf,CAAe,CAAC,CAAC;gBACjE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gBACnD,IAAI,CAAC,OAAO,CAAC,UAAA,GAAG;oBACd,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;wBACrB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE;4BAChC,OAAO,CAAC,GAAG,CAAC,SAAO,GAAG,oBAAiB,CAAC,CAAC;4BACzC,OAAO,GAAG,KAAK,CAAC;yBACjB;qBACF;yBAAM;wBACL,IAAI,EAAE,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,EAAE;4BAC/B,OAAO,CAAC,GAAG,CAAC,SAAO,GAAG,kBAAe,CAAC,CAAC;4BACvC,OAAO,GAAG,KAAK,CAAC;yBACjB;qBACF;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,OAAO,EAAE;oBACX,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,EAAE,CAAC;oBACV,OAAO;iBACR;gBACD,KAAK,EAAE,CAAC;gBACR,IAAI,KAAK,GAAG,UAAU,IAAI,SAAS,EAAE;oBACnC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,IAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAG,CAAC,UAAK,YAAY,CAAC,CAAC,CAAG,EAA1B,CAA0B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC5E,MAAM,CAAC,IAAI,CAAC,mCAAiC,cAAgB,CAAC,CAAC;oBAC/D,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW,EAAE,IAAY,EAAE,UAAwB,EAAE,SAAwB;IAAlD,2BAAA,EAAA,gBAAwB;IAAE,0BAAA,EAAA,gBAAwB;IAC3G,OAAO,IAAI,OAAO,CAA8B,UAAA,OAAO;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,IAAM,QAAQ,GAAG,WAAW,CAAC;YAC3B,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,UAAA,EAAE;gBAC3B,IAAM,QAAQ,GAAgC,EAAE,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,GAAG,EAAb,CAAa,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;oBAC/C,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;gBACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACP,IAAM,IAAI,GAAgC,EAAE,CAAC;;oBAE3C,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAG,CAAC;oBAClC,EAAE,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,EAAtB,CAAsB,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC;wBAC1C,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;oBACtC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAA,CAAC,IAAI,OAAA,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAhB,CAAgB,CAAC,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;gBALrB,OAAO,QAAQ,CAAC,MAAM;;iBAMrB;gBACD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC1C,IAAM,OAAO,GAAG,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC;gBACrC,IAAI,OAAO,EAAE;oBACX,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,CAAC;oBACd,OAAO;iBACR;gBACD,KAAK,EAAE,CAAC;gBACR,IAAI,KAAK,GAAG,UAAU,IAAI,SAAS,EAAE;oBACnC,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,kCAAgC,IAAI,kBAAa,IAAI,CAAC,MAAQ,CAAC,CAAC;iBAC7E;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,UAAU,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE;IAChC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,EAAyB;YAAxB,SAAS,QAAA,EAAE,YAAY,QAAA;QAC7E,QAAQ,CAAC,kCAAgC,SAAS,yBAAoB,YAAY,MAAG,EAAE;YACrF,QAAQ,CAAC,MAAM,EAAE;gBACf,EAAE,CAAC,iCAAiC,EAAE,UAAU,IAAI;oBAClD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBAC7E,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,cAAM,OAAA,IAAI,EAAE,EAAN,CAAM,CAAC,CAAC;oBAC9B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,8BAA8B,EAAE,UAAU,IAAgB;oBAC3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBAC7E,sBAAsB;oBACtB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;oBAC/B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;oBACzB,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;oBACxC,sBAAsB,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAA,IAAI;wBACtD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,CAAC;wBAC1D,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,gBAAgB,CAAC,CAAC;wBACjE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,UAAU,CAAC,CAAC;wBAC3D,IAAI,CAAC,IAAI,EAAE,CAAC;wBACZ,IAAM,YAAY,GAAkB,EAAE,CAAC;wBACvC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBAClC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBAClC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;wBAClC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;4BACd,mBAAmB,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;gCACjD,IAAI,EAAE,CAAC;4BACT,CAAC,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,QAAQ,EAAE;gBACjB,EAAE,CAAC,oEAAoE,EAAE,UAAC,IAAI;oBAC5E,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBAC7E,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAnB,CAAmB,CAAC,CAAC;oBACzC,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;oBACvC,MAAM,CAAC,YAAY,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;oBAC7C,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;wBACd,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;oBACH,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,CAAC,CAAC,CAAC;gBACH,EAAE,CAAC,qEAAqE,EAAE,UAAC,IAAI;oBAC7E,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBACvE,IAAK,CAAC,MAAM,CAAC;wBACjB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;4BAChB,MAAM,CAAC,MAAM,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAjB,CAAiB,CAAC,CAAC;4BACvC,IAAI,EAAE,CAAC;wBACT,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,OAAO,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,qBAAqB,EAAE;gBAC9B,EAAE,CAAC,+DAA+D,EAAE,UAAU,IAAI;oBAChF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;oBAC9E,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;wBAC/B,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;qBAC3B;oBAED,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;oBAC3D,IAAM,IAAI,GAAG,EAAE,CAAC,YAAY,CAAI,OAAO,CAAC,GAAG,CAAC,MAAM,wBAAqB,CAAC,CAAC;oBACzE,EAAE,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;oBAEtC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE;wBACjC,wCAAwC;wBACxC,OAAO;qBACR;oBACD,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,aAAa,EAAE,uBAAuB,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBACtG,IAAI,MAAM,GAAG,EAAE,CAAC;oBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;wBACnB,MAAM,IAAI,IAAI,CAAC;oBACjB,CAAC,CAAC,CAAC;oBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;wBACd,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9C,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,KAAK,EAAE;gBACd,EAAE,CAAC,+CAA+C,EAAE,UAAU,IAAI;oBAChE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAC,CAAC,CAAC;oBAC9G,IAAI,MAAM,GAAG,EAAE,CAAC;oBAChB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;wBACnB,MAAM,IAAI,IAAI,CAAC;oBACjB,CAAC,CAAC,CAAC;oBACH,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;wBACd,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;wBACtC,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,UAAU,EAAE;gBACnB,EAAE,CAAC,uCAAuC,EAAE,UAAU,IAAI;oBACxD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBACpF,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;wBACnB,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBAC5B,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBAEH,EAAE,CAAC,2CAA2C,EAAE,UAAU,IAAI;oBAC5D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBACtF,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,UAAC,IAAI;wBACnB,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;wBAC5B,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,OAAO,EAAE;gBAChB,EAAE,CAAC,qBAAqB,EAAE,UAAU,IAAI;oBACtC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACpB,IAAM,IAAI,GAAG,IAAI,iCAAe,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;oBAC7E,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACrB,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE;wBACd,IAAI,EAAE,CAAC;oBACT,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;CACJ"}